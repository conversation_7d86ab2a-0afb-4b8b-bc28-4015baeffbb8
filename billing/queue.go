package billing

import (
	"context"
	"time"

	"github.com/luispater/ai-router/models"
)

// BillingQueue defines the interface for a billing queue
type BillingQueue interface {
	// Enqueue adds a usage record to the billing queue
	Enqueue(ctx context.Context, usage models.Usage) error

	// Dequeue removes and returns a usage record from the billing queue
	// It blocks until a record is available or the context is canceled
	Dequeue(ctx context.Context) (*models.Usage, error)

	// Close closes the billing queue
	Close() error
}

// BillingProcessor processes billing information from the queue and stores it in the database
type BillingProcessor struct {
	queue BillingQueue
	db    interface{} // This will be *gorm.DB in the implementation
	done  chan struct{}
}

// NewBillingProcessor creates a new billing processor
func NewBillingProcessor(queue BillingQueue, db interface{}) *BillingProcessor {
	return &BillingProcessor{
		queue: queue,
		db:    db,
		done:  make(chan struct{}),
	}
}

// Start starts the billing processor
func (p *BillingProcessor) Start(ctx context.Context) {
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			case <-p.done:
				return
			default:
				// Process billing information
				usage, err := p.queue.Dequeue(ctx)
				if err != nil {
					// Log the error and continue
					continue
				}

				// Store the usage record in the database
				p.processUsage(usage)

				// Sleep for a short time to avoid CPU spinning
				time.Sleep(10 * time.Millisecond)
			}
		}
	}()
}

// Stop stops the billing processor
func (p *BillingProcessor) Stop() {
	close(p.done)
}

// processUsage processes a usage record and stores it in the database
func (p *BillingProcessor) processUsage(usage *models.Usage) {
	// This will be implemented in the concrete implementation
}