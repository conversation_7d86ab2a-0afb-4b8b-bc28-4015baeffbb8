package billing

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/luispater/ai-router/config"
	"github.com/luispater/ai-router/models"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// RedisBillingQueue implements the BillingQueue interface using Redis
type RedisBillingQueue struct {
	client *redis.Client
	key    string
}

// GetRedisClient creates and returns a Redis client
func GetRedisClient(cfg *config.RedisConfig) *redis.Client {
	client := redis.NewClient(&redis.Options{
		Addr:     cfg.URL,
		Password: cfg.Password,
		DB:       cfg.DB,
	})

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := client.Ping(ctx).Result()
	if err != nil {
		log.Printf("Failed to connect to Redis: %v", err)
		return nil
	}

	return client
}

// NewRedisBillingQueue creates a new Redis-based billing queue
func NewRedisBillingQueue(addr, password string, db int, key string) (*RedisBillingQueue, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     addr,
		Password: password,
		DB:       db,
	})

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := client.Ping(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &RedisBillingQueue{
		client: client,
		key:    key,
	}, nil
}

// Enqueue adds a usage record to the billing queue
func (q *RedisBillingQueue) Enqueue(ctx context.Context, usage models.Usage) error {
	// Convert the usage record to JSON
	data, err := json.Marshal(usage)
	if err != nil {
		return fmt.Errorf("failed to marshal usage record: %w", err)
	}

	// Add the JSON data to the Redis list
	err = q.client.RPush(ctx, q.key, data).Err()
	if err != nil {
		return fmt.Errorf("failed to add usage record to queue: %w", err)
	}

	return nil
}

// Dequeue removes and returns a usage record from the billing queue
func (q *RedisBillingQueue) Dequeue(ctx context.Context) (*models.Usage, error) {
	// Remove and get the first element from the Redis list
	// BLPOP blocks until an element is available or the timeout is reached
	result, err := q.client.BLPop(ctx, 0, q.key).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get usage record from queue: %w", err)
	}

	// The result is a slice of strings: [key, value]
	if len(result) < 2 {
		return nil, fmt.Errorf("unexpected result from Redis: %v", result)
	}

	// Parse the JSON data
	var usage models.Usage
	err = json.Unmarshal([]byte(result[1]), &usage)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal usage record: %w", err)
	}

	return &usage, nil
}

// Close closes the Redis client
func (q *RedisBillingQueue) Close() error {
	return q.client.Close()
}

// RedisBillingProcessor implements the BillingProcessor with a concrete implementation
type RedisBillingProcessor struct {
	BillingProcessor
	db *gorm.DB
}

// NewRedisBillingProcessor creates a new Redis-based billing processor
func NewRedisBillingProcessor(queue BillingQueue, db *gorm.DB) *RedisBillingProcessor {
	processor := &RedisBillingProcessor{
		BillingProcessor: *NewBillingProcessor(queue, db),
		db:               db,
	}
	return processor
}

// processUsage processes a usage record and stores it in the database
func (p *RedisBillingProcessor) processUsage(usage *models.Usage) {
	// Store the usage record in the database
	result := p.db.Create(usage)
	if result.Error != nil {
		log.Printf("Failed to store usage record: %v", result.Error)
	} else {
		log.Printf("Stored usage record: ID=%d, Type=%s, Cost=%f", usage.ID, usage.RequestType, usage.Cost)
	}
}