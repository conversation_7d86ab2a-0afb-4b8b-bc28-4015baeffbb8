package billing

import (
	"fmt"

	"github.com/luispater/ai-router/config"
	"gorm.io/gorm"
)

// InitBillingQueue initializes the Redis billing queue
func InitBillingQueue(cfg *config.RedisConfig, db *gorm.DB) (BillingQueue, error) {
	// Create a new Redis billing queue
	queue, err := NewRedisBillingQueue(cfg.URL, cfg.Password, cfg.DB, cfg.BillingQueueKey)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize billing queue: %w", err)
	}

	return queue, nil
}