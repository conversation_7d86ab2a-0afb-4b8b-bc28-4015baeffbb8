package billing

import (
	"context"
	"log"
	"net/http"
	"time"

	"github.com/luispater/ai-router/models"
	"github.com/luispater/ai-router/provider"
)

// Global billing queue instance
var globalBillingQueue BillingQueue

// SetGlobalBillingQueue sets the global billing queue instance
func SetGlobalBillingQueue(queue BillingQueue) {
	globalBillingQueue = queue
}

// GetGlobalBillingQueue returns the global billing queue instance
func GetGlobalBillingQueue() BillingQueue {
	return globalBillingQueue
}

// RecordUsage records API usage for billing
// It creates a usage record and enqueues it in the billing queue
func RecordUsage(
	apiKey models.APIKey,
	requestType string,
	promptTokens, outputTokens, totalTokens int,
	processingMs int,
	statusCode int,
	errorMessage string,
	cost float64,
	modelID uint,
) {
	if globalBillingQueue == nil {
		log.Println("Warning: Billing queue not initialized, usage not recorded")
		return
	}

	// Create usage record
	usage := models.Usage{
		UserID:       apiKey.UserID,
		APIKeyID:     apiKey.ID,
		RequestType:  requestType,
		PromptTokens: promptTokens,
		OutputTokens: outputTokens,
		TotalTokens:  totalTokens,
		ProcessingMs: processingMs,
		StatusCode:   statusCode,
		ErrorMessage: errorMessage,
		Cost:         cost,
		ModelID:      modelID,
		CreatedAt:    time.Now(),
	}

	// Enqueue the usage record
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := globalBillingQueue.Enqueue(ctx, usage)
	if err != nil {
		log.Printf("Failed to enqueue usage record: %v", err)
	}
}

// RecordChatUsage records chat API usage for billing
func RecordChatUsage(
	apiKey models.APIKey,
	request []byte,
	usage provider.Usage,
	startTime time.Time,
	modelID uint,
) {
	// Calculate processing time
	processingMs := int(time.Since(startTime).Milliseconds())

	// Default values
	promptTokens := 0
	outputTokens := 0
	totalTokens := 0
	statusCode := http.StatusOK
	errorMessage := ""
	cost := 0.0

	// If we have a response, add token counts
	promptTokens = usage.PromptTokens
	outputTokens = usage.CompletionTokens
	totalTokens = usage.TotalTokens
	// Cost calculation could be added here based on model pricing

	RecordUsage(
		apiKey,
		"chat",
		promptTokens,
		outputTokens,
		totalTokens,
		processingMs,
		statusCode,
		errorMessage,
		cost,
		modelID,
	)
}

// RecordImageUsage records image API usage for billing
func RecordImageUsage(
	apiKey models.APIKey,
	requestType string,
	startTime time.Time,
	modelID uint,
) {
	// Calculate processing time
	processingMs := int(time.Since(startTime).Milliseconds())

	RecordUsage(
		apiKey,
		requestType,
		0, // No tokens for image requests
		0,
		0,
		processingMs,
		http.StatusOK,
		"",
		0.0, // Cost calculation could be added here
		modelID,
	)
}

// RecordFileUsage records file API usage for billing
func RecordFileUsage(
	apiKey models.APIKey,
	purpose string,
	startTime time.Time,
	modelID uint,
) {
	// Calculate processing time
	processingMs := int(time.Since(startTime).Milliseconds())

	RecordUsage(
		apiKey,
		"file_upload",
		0, // No tokens for file uploads
		0,
		0,
		processingMs,
		http.StatusOK,
		"",
		0.0, // Cost calculation could be added here
		modelID,
	)
}
