<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Router For Me</title>
    <link rel="stylesheet" href="/static/index.css" type="text/css" />
</head>
<body>
<div class="top-notice">
    We've updated our <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>. By continuing to use the service, you agree to these revised terms.
</div>

<header>
    <div class="container">
        <!-- <a href="#" class="logo">Router For <span class="logo-accent">Me</span></a> -->
        <img class="logo-img" src="/static/logo.svg" alt="Router For Me"/>
        <nav>
            <ul>
                
            </ul>
        </nav>
        <div class="auth-buttons">
            <button>Models</button>
            <a href="mailto:<EMAIL>?subject=Sign%20Up&body=Send%20this%20email%20using%20your%20main%20email%20address%20to%20sign%20up%20for%20router-for.me%20and%20wait%2015%20seconds%20for%20our%20reply.">
                <button class="primary">Sign Up</button>
            </a>
        </div>
    </div>
</header>

<section class="hero">
    <div class="container">
        <h1>The Unified<br>Interface For LLMs</h1>
        <p class="subtitle">Better prices, better uptime, no subscription.</p>
        <div class="hero-buttons">
            <a href="mailto:<EMAIL>?subject=Sign%20Up&body=Send%20this%20email%20using%20your%20main%20email%20address%20to%20sign%20up%20for%20router-for.me%20and%20wait%2015%20seconds%20for%20our%20reply."><button class="signup-btn">Sign Up for Free</button></a>
            <a href="#"><button class="docs-btn">Read Docs</button></a>
        </div>
        <div class="hero-model-cards-container">
            <div class="model-card">
                <div class="model-info">
                    <div class="model-name">Meta: Llama 4 Maverick 17B 128E Instruct</div> <div class="model-specs">
                    <span>Context: 256K</span> <span>Output: 256K</span>
                </div>
                </div>
                <div class="model-pricing">
                    <div class="price">Free</div> <div class="tokens">$2.6 / 1M tokens</div>
                </div>
            </div>
            <div class="model-card">
                <div class="model-info">
                    <div class="model-name">DeepSeek: DeepSeek v3 0324</div> <div class="model-specs">
                    <span>Context: 164K</span> <span>Output: 164K</span>
                </div>
                </div>
                <div class="model-pricing">
                    <div class="price">Free</div> <div class="tokens">$1.2 / 1M tokens</div>
                </div>
            </div>
            <div class="model-card">
                <div class="model-info">
                    <div class="model-name">QWen: Qwen3 235B A22B</div> <div class="model-specs">
                    <span>Context: 41K</span> <span>Output: 41K</span>
                </div>
                </div>
                <div class="model-pricing">
                    <div class="price">Free</div> <div class="tokens">$0.60 / 1M tokens</div>
                </div>
            </div>
            <div class="model-card">
                <div class="model-info">
                    <div class="model-name">DeepSeek: DeepSeek R1</div> <div class="model-specs">
                    <span>Context: 164K</span> <span>Output: 164K</span>
                </div>
                </div>
                <div class="model-pricing">
                    <div class="price">Free</div> <div class="tokens">$2.50 / 1M tokens</div>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="stats">
    <div class="container">
        <div class="stat-item">
            <h2>7.9T</h2>
            <p>Tokens Processed</p>
        </div>
        <div class="stat-item">
            <h2>2M</h2>
            <p>Developers</p> </div>
        <div class="stat-item">
            <h2>50+</h2>
            <p>Unique Models</p> </div>
        <div class="stat-item">
            <h2>300+</h2>
            <p>Apps & Services</p> </div>
    </div>
</section>

<section class="how-to-start">
    <div class="container">
        <h2 class="section-title">Get Started in Minutes</h2>
        <div class="steps-container">
            <div class="step-card">
                <div class="step-header">
                    <div class="step-number">1</div>
                    <h3>Send Email</h3>
                </div>
                <p>Create an account with your email. <NAME_EMAIL></p>
            </div>
            <div class="step-card">
                <div class="step-header">
                    <div class="step-number">2</div>
                    <h3>Reply Email</h3>
                </div>
                <p>Reply to our email. Finish signing up.</p>
            </div>
            <div class="step-card">
                <div class="step-header">
                    <div class="step-number">3</div>
                    <h3>Create your API key by Email</h3>
                </div>
                <p>Generate an API key and start building. Use it as a drop-in replacement.</p>
            </div>
        </div>
    </div>
</section>

<section class="features">
    <div class="container">
        <div class="feature-card">
            <div class="icon-placeholder">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244" />
                </svg>
            </div>
            <h3>One API for Any Model</h3>
            <p>Access the latest open-source and proprietary models from a single API. No need to manage multiple API keys or request formats.</p>
            <a href="#">Browse all models →</a>
        </div>
        <div class="feature-card">
            <div class="icon-placeholder">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="m3.75 13.5 10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75Z" />
                </svg>
            </div>
            <h3>Higher Availability</h3>
            <p>Automatic failover to other models or providers if a model is unavailable. Maximize uptime for your application.</p>
            <a href="#">Learn more →</a>
        </div>
        <div class="feature-card">
            <div class="icon-placeholder">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z" />
                </svg>
            </div>
            <h3>Price and Performance</h3>
            <p>Compare models by price and performance. Optimize your spend and latency with our unified interface.</p>
            <a href="#">Learn more →</a>
        </div>
        <div class="feature-card">
            <div class="icon-placeholder">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z" />
                </svg>
            </div>
            <h3>Custom Data Policies</h3>
            <p>Route requests to specific regions or providers to meet data residency needs. Full control over your data flow.</p>
            <a href="#">View docs →</a>
        </div>
    </div>
</section>

<footer>
    <div class="container">
        <div class="footer-left">
            <div class="copyright">© 2023 – {{.currentYear}} Router For Me.</div>
            <div class="social-links-footer">
            </div>
        </div>
        <div class="footer-links">
            <ul>
                <li><a href="#">Status</a></li>
                <li><a href="#">Announcements</a></li>
                <li><a href="#">Partners</a></li>
                <li><a href="#">Careers</a></li>
                <li><a href="#">Pricing</a></li>
                <li><a href="#">Privacy</a></li>
                <li><a href="#">Terms</a></li>
            </ul>
        </div>
    </div>
</footer>

</body>
</html>
