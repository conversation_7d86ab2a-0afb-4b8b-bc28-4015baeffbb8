package config

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// Config 表示应用程序的配置
type Config struct {
	// Server 服务器配置
	Server ServerConfig `yaml:"server"`
	// Database 数据库配置
	Database DatabaseConfig `yaml:"database"`
	// Redis Redis 配置
	Redis RedisConfig `yaml:"redis"`
}

// ServerConfig 表示服务器的配置
type ServerConfig struct {
	// Port 监听端口
	Port string `yaml:"port"`
	// ShutdownTimeout 关闭超时时间
	ShutdownTimeout time.Duration `yaml:"shutdown_timeout"`
}

// DatabaseConfig 表示数据库的配置
type DatabaseConfig struct {
	// URL 数据库连接 URL
	URL string `yaml:"url"`
	// MaxIdleConns 最大空闲连接数
	MaxIdleConns int `yaml:"max_idle_conns"`
	// MaxOpenConns 最大打开连接数
	MaxOpenConns int `yaml:"max_open_conns"`
	// ConnMaxLifetime 连接最大生命周期
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
}

// RedisConfig 表示 Redis 的配置
type RedisConfig struct {
	// URL Redis 连接 URL
	URL string `yaml:"url"`
	// Password Redis 密码
	Password string `yaml:"password"`
	// DB Redis 数据库编号
	DB int `yaml:"db"`
	// BillingQueueKey 计费队列的键名
	BillingQueueKey string `yaml:"billing_queue_key"`
}

// / LoadConfig 从指定的文件加载配置
func LoadConfig(configFile string) (*Config, error) {
	// Read the configuration file
	// 读取配置文件
	data, err := os.ReadFile(configFile)
	// 如果读取文件失败
	if err != nil {
		// 返回错误
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// Parse the YAML data
	// 解析 YAML 数据
	var config Config
	// 如果解析 YAML 数据失败
	if err = yaml.Unmarshal(data, &config); err != nil {
		// 返回错误
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// Apply environment variable overrides
	// 应用环境变量覆盖
	applyEnvironmentOverrides(&config)

	// 返回配置
	return &config, nil
}

// / applyEnvironmentOverrides 将环境变量覆盖应用到配置中
func applyEnvironmentOverrides(config *Config) {
	// Server settings
	// 服务器设置
	// 如果环境变量 PORT 存在
	if port := os.Getenv("PORT"); port != "" {
		// 使用环境变量的值覆盖配置
		config.Server.Port = port
	}

	// Database settings
	// 数据库设置
	// 如果环境变量 DATABASE_URL 存在
	if dbURL := os.Getenv("DATABASE_URL"); dbURL != "" {
		// 使用环境变量的值覆盖配置
		config.Database.URL = dbURL
	}

	// Redis settings
	// Redis 设置
	// 如果环境变量 REDIS_URL 存在
	if redisURL := os.Getenv("REDIS_URL"); redisURL != "" {
		// 使用环境变量的值覆盖配置
		config.Redis.URL = redisURL
	}
	// 如果环境变量 REDIS_PASSWORD 存在
	if redisPassword := os.Getenv("REDIS_PASSWORD"); redisPassword != "" {
		// 使用环境变量的值覆盖配置
		config.Redis.Password = redisPassword
	}
}
