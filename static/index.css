/* 基本重置和全局样式 */
body, h1, h2, h3, h4, p, ul, li, button, a, div, section, header, footer, nav {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"; /* 系统字体栈 */
    color: #111827; /* 深灰色文本，接近图片 */
}

body {
    background-color: #ffffff;
    line-height: 1.6;
    font-size: 16px; /* 基准字体大小 */
}

.container {
    max-width: 1280px; /* 稍微加宽以匹配图片感觉 */
    margin: 0 auto;
    padding: 0 24px; /* 左右内边距 */
}

/* 顶部通知栏 */
.top-notice {
    background-color: #dbf4ff; /* 淡紫色背景 */
    color: #039ce0; /* 紫色文字 */
    padding: 8px 0;
    text-align: center;
    font-size: 0.875em;
}
.top-notice a {
    color: #039ce0;
    font-weight: 500;
    text-decoration: underline;
}

/* 头部导航 */
header {
    padding: 16px 0;
    border-bottom: 1px solid #e5e7eb; /* 浅灰色边框 */
    background-color: #fff;
    position: sticky; /* 使导航栏在滚动时固定在顶部 */
    top: 0;
    z-index: 1000;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.75em; /* 增大 Logo 尺寸 */
    font-weight: 700; /* 加粗 */
    color: #000;
    text-decoration: none;
}

.logo-img {
    width: 275px;
    height: 50px;
}

.logo-accent {
    color: #039ce0; /* 紫色 */
}

nav ul {
    list-style: none;
    display: flex;
    align-items: center;
}

nav ul li {
    margin-left: 28px; /* 调整导航项间距 */
}

nav ul li a {
    text-decoration: none;
    color: #4b5563; /* 中灰色导航链接 */
    font-weight: 500;
    font-size: 0.95em;
}

nav ul li a:hover {
    color: #039ce0;
}

.auth-buttons button {
    margin-left: 12px;
    padding: 8px 18px; /* 调整按钮内边距 */
    border: 1px solid #d1d5db; /* 边框颜色 */
    border-radius: 8px; /* 圆角 */
    background-color: #fff;
    cursor: pointer;
    font-weight: 600; /* 字体加粗 */
    font-size: 0.9em;
    transition: background-color 0.2s ease, border-color 0.2s ease;
}
.auth-buttons button:hover {
    border-color: #9ca3af;
}

.auth-buttons button.primary {
    background-color: #039ce0;
    color: #fff;
    border-color: #039ce0;
}
.auth-buttons button.primary:hover {
    background-color: #068ecb; /* 深一点的紫色 */
    border-color: #068ecb;
}

/* 英雄区域 */
.hero {
    padding: 40px 0; /* 调整上下内边距 */
    text-align: center;
    background-color: #fff; /* 背景改回白色 */
}

.hero h1 {
    font-size: 3.75rem; /* 显著增大标题字体 */
    font-weight: 700; /* 标题加粗 */
    margin-bottom: 24px;
    color: #111827;
    line-height: 1.15; /* 调整行高 */
}

.hero p.subtitle { /* 专门为副标题添加类名 */
    font-size: 1.25rem; /* 增大副标题字体 */
    color: #4b5563; /* 副标题颜色 */
    margin-bottom: 40px; /* 增大与按钮的间距 */
    max-width: 650px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons button {
    padding: 14px 32px; /* 增大按钮内边距 */
    font-size: 1rem; /* 按钮字体大小 */
    border-radius: 8px;
    cursor: pointer;
    margin: 0 12px;
    font-weight: 600;
    transition: background-color 0.3s ease, transform 0.1s ease;
    border: 1px solid transparent; /* 预留边框空间 */
}
.hero-buttons button:active {
    transform: scale(0.98);
}

.hero-buttons .signup-btn {
    background-color: #039ce0;
    color: #fff;
}
.hero-buttons .signup-btn:hover {
    background-color: #068ecb;
}

.hero-buttons .docs-btn {
    background-color: #fff;
    color: #374151; /* 深灰色文字 */
    border: 1px solid #d1d5db; /* 边框 */
}
.hero-buttons .docs-btn:hover {
    background-color: #f9fafb; /* 鼠标悬停背景色 */
    border-color: #9ca3af;
}

.hero-model-cards-container { /* 模型卡片区域 */
    background-color: #fff; /* 卡片容器背景，原图似乎没有明显背景 */
    border-radius: 16px; /* 更大的圆角 */
    padding: 20px; /* 内边距 */
    max-width: 520px; /* 限制最大宽度 */
    margin: 60px auto 0; /* 与按钮的间距 */
    /* box-shadow: 0 8px 25px rgba(0,0,0,0.08); /* 更柔和的阴影 */
    /* border: 1px solid #e5e7eb; /* 浅边框 */
    /* 上述注释掉的样式是为了更接近原图的无明显边框和阴影感 */
    display: flex;
    flex-direction: column;
    gap: 16px; /* 卡片之间的间距 */
}
.model-card {
    border: 1px solid #e5e7eb; /* 卡片边框 */
    background-color: #ffffff;
    border-radius: 12px; /* 卡片圆角 */
    padding: 20px; /* 卡片内边距 */
    display: flex;
    justify-content: space-between;
    align-items: flex-start; /* 顶部对齐 */
    text-align: left;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04); /* 轻微阴影 */
}
.model-info {
    flex-grow: 1;
}
.model-name {
    font-weight: 600;
    font-size: 1.05em;
    color: #1f2937; /* 较深的模型名称颜色 */
    margin-bottom: 6px; /* 与详情的间距 */
}
.model-specs {
    font-size: 0.8em;
    color: #6b7280; /* 规格文字颜色 */
    display: flex;
    gap: 8px; /* 规格之间的间距 */
}
.model-specs span {
    background-color: #f3f4f6; /* 浅灰色背景 */
    padding: 2px 6px;
    border-radius: 4px;
}
.model-pricing {
    text-align: right;
    white-space: nowrap; /* 防止价格换行 */
    margin-left: 16px; /* 与左侧信息的间距 */
}
.model-pricing .price {
    font-weight: 600; /* 价格加粗 */
    font-size: 1.1em;
    color: #10b981; /* 绿色价格，更鲜艳些 */
    margin-bottom: 2px;
}
.model-pricing .tokens {
    text-decoration:line-through;
    font-size: 0.75em; /* 调小tokens文字 */
    color: #6b7280;
}

/* 统计数据区域 */
.stats {
    padding: 0 0 60px 0; /* 调整内边距 */
    background-color: #fff; /* 背景为白色 */
}
.stats .container {
    display: flex;
    justify-content: space-around; /* 均匀分布 */
    text-align: center;
    flex-wrap: wrap; /* 允许换行 */
}
.stat-item {
    padding: 20px;
    min-width: 180px; /* 保证一定宽度 */
}
.stat-item h2 {
    font-size: 2.75rem; /* 增大数字 */
    color: #039ce0;
    margin-bottom: 4px; /* 减小与文字的间距 */
    font-weight: 700; /* 数字加粗 */
}
.stat-item p {
    font-size: 1rem; /* 描述文字大小 */
    color: #4b5563; /* 描述文字颜色 */
}

/* "如何开始" 步骤区域 */
.how-to-start {
    padding: 80px 0;
    background-color: #f9fafb; /* 非常浅的灰色背景 */
}
.how-to-start h2.section-title {
    text-align: center;
    font-size: 2.25rem; /* 增大标题 */
    margin-bottom: 60px; /* 增大与卡片的间距 */
    color: #111827;
    font-weight: 600;
}
.steps-container {
    display: flex;
    justify-content: space-between;
    gap: 30px;
}
.step-card {
    background-color: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 12px; /* 增大圆角 */
    padding: 32px; /* 增大内边距 */
    flex-basis: calc(33.333% - 20px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    text-align: left; /* 文字左对齐 */
}
.step-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}
.step-number {
    background-color: #d3f1fe; /* 更淡的紫色背景 */
    color: #039ce0;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9em;
    margin-right: 12px; /* 与标题的间距 */
}
.step-card h3 {
    font-size: 1.25rem; /* 增大卡片标题 */
    color: #1f2937;
    font-weight: 600;
}
.step-card p {
    font-size: 0.95rem;
    color: #4b5563;
    margin-bottom: 20px; /* 与图片的间距 */
    line-height: 1.5;
}
.step-card img.step-image-placeholder {
    width: 100%;
    height: auto;
    border: 1px solid #e5e7eb; /* 图片边框 */
    border-radius: 8px;
    display: block;
    background-color: #f3f4f6; /* 占位符背景 */
}

/* 特性区域 */
.features {
    padding: 80px 0;
    background-color: #fff;
}
.features .container {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 固定两列 */
    gap: 40px; /* 增大间距 */
}
.feature-card {
    background-color: #f9fafb; /* 卡片背景 */
    padding: 32px; /* 增大内边距 */
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    display: flex; /* 使用 flex 布局图标和文字 */
    flex-direction: column; /* 垂直排列 */
    align-items: flex-start; /* 左对齐 */
}
.feature-card .icon-placeholder {
    width: 48px; /* 图标尺寸 */
    height: 48px;
    background-color: #d3f1fe; /* 淡紫色背景 */
    border-radius: 10px; /* 图标背景圆角 */
    margin-bottom: 20px; /* 与标题的间距 */
    display: flex;
    align-items: center;
    justify-content: center;
    color: #039ce0;
    /* 使用SVG作为占位符 */
}
.feature-card .icon-placeholder svg {
    width: 24px;
    height: 24px;
    stroke: currentColor; /* 使SVG颜色与父元素color一致 */
}

.feature-card h3 {
    font-size: 1.5rem; /* 增大标题 */
    margin-bottom: 12px;
    color: #111827;
    font-weight: 600;
}
.feature-card p {
    font-size: 1rem;
    color: #4b5563;
    margin-bottom: 16px;
    line-height: 1.6;
}
.feature-card a {
    color: #039ce0;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
}
.feature-card a:hover {
    text-decoration: underline;
}

/* "Top Apps" 区域 */
.top-apps {
    padding: 80px 0;
    background-color: #f9fafb;
}
.top-apps-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}
.top-apps h2.section-title {
    font-size: 2.25rem;
    color: #111827;
    font-weight: 600;
    margin-bottom: 0; /* 移除原有底部边距 */
}
.filter-dropdown-placeholder { /* 模拟下拉菜单 */
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9em;
    color: #4b5563;
    background-color: #fff;
}
.apps-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 两列布局 */
    gap: 24px; /* 应用项之间的间距 */
}
.app-item {
    background-color: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
}
.app-rank {
    font-size: 1em;
    font-weight: 600;
    color: #6b7280; /* 排名颜色 */
    margin-right: 16px;
    min-width: 24px;
}
.app-icon-placeholder {
    width: 40px; /* 图标尺寸 */
    height: 40px;
    background-color: #f3f4f6; /* 图标背景 */
    border-radius: 8px; /* 图标圆角 */
    margin-right: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9em;
    color: #9ca3af; /* 图标内文字颜色 */
    font-weight: 500;
}
.app-details h4 {
    font-size: 1.1rem; /* 应用名称字体 */
    margin-bottom: 4px;
    color: #1f2937;
    font-weight: 600;
}
.app-details p {
    font-size: 0.875rem; /* 用户数字体 */
    color: #6b7280;
}

/* 页脚 */
footer {
    padding: 60px 0; /* 增大页脚内边距 */
    background-color: #fff;
    border-top: 1px solid #e5e7eb;
    font-size: 0.9em;
}
footer .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}
.footer-left {
    display: flex;
    align-items: center;
    gap: 12px; /* © 和社交图标的间距 */
}
.copyright {
    color: #6b7280; /* 版权文字颜色 */
}
.social-links-footer a { /* 页脚社交链接 */
    color: #6b7280;
    text-decoration: none;
    margin-left: 10px; /* 社交图标间距 */
    font-weight: 500; /* 模拟图标的文字 */
}
.social-links-footer a:hover {
    color: #039ce0;
}

.footer-links ul {
    list-style: none;
    display: flex;
    flex-wrap: wrap; /* 允许链接换行 */
}
.footer-links ul li {
    margin-left: 24px; /* 链接间距 */
}
.footer-links ul li:first-child {
    margin-left: 0;
}
.footer-links ul li a {
    text-decoration: none;
    color: #4b5563; /* 链接颜色 */
    font-weight: 500;
}
.footer-links ul li a:hover {
    color: #039ce0;
}

/* 响应式调整 */
@media (max-width: 1024px) { /* 调整断点 */
    .features .container {
        grid-template-columns: 1fr; /* 中等屏幕特性单列 */
    }
    .apps-grid {
        grid-template-columns: 1fr; /* 中等屏幕应用单列 */
    }
    .steps-container {
        flex-direction: column;
        align-items: center;
    }
    .step-card {
        flex-basis: auto; /* 移除 flex-basis */
        width: 80%;
        max-width: 450px; /* 限制最大宽度 */
        margin-bottom: 24px;
    }
    .step-card:last-child {
        margin-bottom: 0;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }
    header .container {
        flex-direction: column;
        align-items: flex-start;
    }
    nav {
        width: 100%;
        margin-top: 12px;
    }
    nav ul {
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
    }
    nav ul li {
        margin-left: 0;
        margin-bottom: 10px;
        width: 100%;
    }
    nav ul li a {
        display: block;
        padding: 8px 0;
    }
    .auth-buttons {
        margin-top: 12px;
        width: 100%;
        display: flex;
    }
    .auth-buttons button {
        flex-grow: 1;
        margin-left: 0;
    }
    .auth-buttons button:first-child {
        margin-right: 8px;
    }

    .hero h1 {
        font-size: 2.5rem; /* 移动端标题 */
    }
    .hero p.subtitle {
        font-size: 1.1rem; /* 移动端副标题 */
    }
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    .hero-buttons button {
        margin: 8px 0;
        width: 90%;
        max-width: 320px;
    }
    .hero-model-cards-container {
        width: 90%;
        padding: 16px;
    }
    .model-card {
        flex-direction: column; /* 模型卡片在小屏幕上垂直排列 */
        align-items: stretch; /* 伸展以填充宽度 */
    }
    .model-pricing {
        text-align: left;
        margin-left: 0;
        margin-top: 10px;
    }

    .stats .container {
        flex-direction: column;
    }
    .stat-item {
        margin-bottom: 20px;
        min-width: auto;
    }
    .stat-item:last-child {
        margin-bottom: 0;
    }

    .step-card {
        width: 90%;
    }
    .top-apps-header {
        flex-direction: column;
        align-items: flex-start;
    }
    .top-apps h2.section-title {
        margin-bottom: 16px;
    }

    footer .container {
        flex-direction: column;
        text-align: center;
    }
    .footer-left {
        margin-bottom: 16px;
        flex-direction: column;
    }
    .social-links-footer {
        margin-top: 8px;
    }
    .footer-links ul {
        justify-content: center;
        margin-top: 10px;
    }
    .footer-links ul li {
        margin: 0 10px 8px; /* 调整移动端页脚链接间距 */
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 2rem;
    }
    .stat-item h2 {
        font-size: 2.25rem;
    }
    .step-card {
        width: 100%;
        padding: 24px;
    }
    .feature-card {
        padding: 24px;
    }
    .feature-card h3 {
        font-size: 1.3rem;
    }
    .feature-card p {
        font-size: 0.95rem;
    }
}