package api

import (
	"bytes"
	"context"
	"fmt"
	"github.com/luispater/ai-router/core"
	"io"
	"log"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	_const "github.com/luispater/ai-router/const"
	"github.com/tidwall/sjson"

	"github.com/gin-gonic/gin"
	"github.com/luispater/ai-router/billing"
	"github.com/luispater/ai-router/models"
	"github.com/luispater/ai-router/provider"
	"github.com/tidwall/gjson"
	"github.com/xeipuuv/gojsonschema"
	"gorm.io/gorm"
)

// lastUsedModelIndex 跟踪每个模型名称的最后使用模型索引，以实现轮询负载均衡
var (
	// lastUsedModelIndex 存储每个模型名称最后使用的索引
	lastUsedModelIndex = make(map[string]int)
	// modelIndexMutexes 存储每个模型名称的互斥锁
	modelIndexMutexes = make(map[string]*sync.Mutex)
	// mutexMapLock 用于保护 modelIndexMutexes 的并发访问
	mutexMapLock = &sync.Mutex{}
)

// schemaLoader 用于加载 JSON 模式
var schemaLoader gojsonschema.JSONLoader

// init 函数在包初始化时执行
func init() {
	// 读取 JSON 模式文件
	schemaData, err := os.ReadFile("json-schema/chat-completions.json")
	// 如果读取文件失败，则触发 panic
	if err != nil {
		panic(err)
	}
	// 创建一个新的基于字节的 JSON 模式加载器
	schemaLoader = gojsonschema.NewBytesLoader(schemaData)
}

// validateRequest 验证请求是否符合 JSON 模式
func validateRequest(request []byte) (*gojsonschema.Result, error) {
	// 创建一个新的基于字节的文档加载器
	documentLoader := gojsonschema.NewBytesLoader(request)
	// 使用模式加载器和文档加载器进行验证
	return gojsonschema.Validate(schemaLoader, documentLoader)
}

// / ChatCompletionHandler 处理聊天补全请求
// / db: 数据库连接
// / providerRegistry: 提供者注册表
func ChatCompletionHandler(db *gorm.DB, providerRegistry map[_const.ProviderType]provider.ProviderFactory) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置响应头，指定内容类型和字符集
		c.Header("Content-Type", "application/json; charset=utf-8")
		// c.Writer.WriteHeaderNow()

		// 从上下文中获取 API 密钥
		apiKeyObj, exists := c.Get("apiKey")
		// 如果 API 密钥不存在，则返回 401 错误
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "API key not found in context", "code": 401})
			return
		}
		// 将 API 密钥对象断言为 models.APIKey 类型
		apiKey := apiKeyObj.(models.APIKey)

		// 获取原始 JSON 数据
		rawJson, err := c.GetRawData()
		// 如果获取数据失败，则返回 400 错误
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid request: %v", err), "code": 400})
			return
		}

		customProvider := make([]string, 0)
		providerResult := gjson.GetBytes(rawJson, "provider")
		if providerResult.Type == gjson.String {
			customProvider = append(customProvider, providerResult.String())
			rawJson, _ = sjson.DeleteBytes(rawJson, "provider")
		} else if providerResult.Type == gjson.JSON {
			// gjson 获取数组数据
			providerResult.ForEach(func(key, value gjson.Result) bool {
				customProvider = append(customProvider, strings.ToLower(value.String()))
				return true
			})
			rawJson, _ = sjson.DeleteBytes(rawJson, "provider")
		} else {
			providerHeader := c.GetHeader("Provider")
			if providerHeader != "" {
				splitProviderHeader := strings.Split(providerHeader, ",")
				for _, item := range splitProviderHeader {
					customProvider = append(customProvider, strings.ToLower(strings.TrimSpace(item)))
				}
			}
		}

		// 验证请求是否符合 JSON 模式
		result, err := validateRequest(rawJson)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "code": 400})
			return
		}
		// 如果验证失败
		if !result.Valid() {
			// 遍历所有错误
			for _, desc := range result.Errors() {
				// 如果错误类型是 required，则返回 400 错误
				if desc.Type() == "required" {
					c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid request: %v", desc.Description()), "code": 400})
					// 如果错误类型是 invalid_type，则返回 400 错误
				} else if desc.Type() == "invalid_type" {
					c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid request: Field `%s` %s", desc.Field(), desc.Description()), "code": 400})
					// 如果错误类型是 string_gte，则返回 400 错误
				} else if desc.Type() == "string_gte" {
					c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid request: Field `%s` %s", desc.Field(), desc.Description()), "code": 400})
					// 其他错误类型，则返回 400 错误
				} else {
					log.Println(desc.Type())
					c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid request"), "code": 400})
				}
				log.Println(string(rawJson))
				return
			}
		}

		// 获取模型名称
		modelNameResult := gjson.GetBytes(rawJson, "model")
		// 如果模型名称不是字符串类型，则返回 400 错误
		if modelNameResult.Type != gjson.String {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid request: %v", err), "code": 400})
			return
		}

		// 从数据库中获取模型
		var providerModels []models.Model

		if len(customProvider) > 0 {
			var providers []models.Provider
			if err = db.Find(&providers).Error; err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Providers not found", "code": 500})
			}
			customProviderID := make([]uint, 0)
			for _, item := range providers {
				if core.InArray(strings.ToLower(item.Name), customProvider) {
					customProviderID = append(customProviderID, item.ID)
				}
			}

			if len(customProviderID) == 0 {
				log.Printf("The specified providers %s don't provide model %s. (Providers not found)", strings.Join(customProvider, ", "), modelNameResult.String())

				c.JSON(http.StatusNotFound, gin.H{
					"error": gin.H{
						"message": fmt.Sprintf("The specified providers don't provide model %s.", modelNameResult.String()),
						"code":    404,
					},
				})
				return
			} else {
				if err = db.Preload("Provider").Where("name = ? AND enabled = true AND provider_id IN ?", modelNameResult.String(), customProviderID).Order("output_price_per_token ASC, input_price_per_token ASC").Find(&providerModels).Error; err != nil || len(providerModels) == 0 {
					// 如果模型未找到，则使用默认模型
					log.Printf("The specified providers %s don't provide model %s.", strings.Join(customProvider, ", "), modelNameResult.String())
					c.JSON(http.StatusNotFound, gin.H{
						"error": gin.H{
							"message": fmt.Sprintf("The specified providers don't provide model %s.", modelNameResult.String()),
							"code":    404,
						},
					})
					return
				}
			}
		} else {
			// 预加载 Provider 关联，并根据模型名称和启用状态查找模型，并根据输出和输入价格排序
			if err = db.Preload("Provider").Where("name = ? AND enabled = true", modelNameResult.String()).Order("output_price_per_token ASC, input_price_per_token ASC").Find(&providerModels).Error; err != nil || len(providerModels) == 0 {
				// 如果模型未找到，则使用默认模型
				log.Printf("Model %s not found", modelNameResult.String())
				c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Model %s not found", modelNameResult.String()), "code": 500})
				return
			}
		}

		modelName := modelNameResult.String()

		// 实现轮询负载均衡
		if len(customProvider) > 0 {
			modelName = fmt.Sprintf("%s-%s", modelName, strings.Join(customProvider, ""))
		}
		// 获取此模型名称的最后使用索引并计算下一个索引
		// 获取或创建此模型的互斥锁
		mutex, exists := modelIndexMutexes[modelName]
		// 如果互斥锁不存在
		if !exists {
			// 加锁
			mutexMapLock.Lock()
			// 创建互斥锁
			mutex = &sync.Mutex{}
			// 添加到互斥锁映射中
			modelIndexMutexes[modelName] = mutex
			// 解锁
			mutexMapLock.Unlock()
		}

		// 使用特定于模型的互斥锁
		mutex.Lock()
		// 获取最后使用的索引
		startIndex := lastUsedModelIndex[modelName]
		// 记录当前正在使用的模型
		currentIndex := (startIndex + 1) % len(providerModels)
		// 更新最后使用的索引
		lastUsedModelIndex[modelName] = currentIndex
		// 解锁
		mutex.Unlock()

		// 重新排序模型数组以从下一个模型开始
		reorderedModels := make([]models.Model, len(providerModels))
		for i := 0; i < len(providerModels); i++ {
			reorderedModels[i] = providerModels[(startIndex+1+i)%len(providerModels)]
		}

		// 遍历重新排序后的模型
		for _, model := range reorderedModels {
			log.Printf("Current Provider: %s\n", model.Provider.Name)
			var factory provider.ProviderFactory
			var ok bool
			// 如果模型不支持 OpenAI 兼容性
			if !model.IsOpenAICompatibility {
				// 获取提供者类型
				providerType := _const.ProviderType(model.ProviderID)
				// 获取提供者工厂
				factory, ok = providerRegistry[providerType]
				// 如果提供者工厂不存在，则返回 500 错误
				if !ok {
					c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Provider factory not found for: %d", providerType), "code": 500})
					return
				}
			} else {
				// openai 兼容性
				// 获取 OpenAI 兼容性提供者工厂
				factory, ok = providerRegistry[_const.ProviderOpenAICompatibility]
				// 如果提供者工厂不存在，则返回 500 错误
				if !ok {
					c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Provider factory not found for: %d", model.ProviderID), "code": 500})
					return
				}
			}

			// 创建提供者实例
			providerInstance, errFactory := factory(db)
			// 如果创建提供者实例失败，则返回 500 错误
			if errFactory != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to create provider: %v", err), "code": 500})
				return
			}

			// 如果模型支持 OpenAI 兼容性
			if model.IsOpenAICompatibility {
				// 如果提供者实例是 OpenAICompatibility 类型
				if instance, isOk := providerInstance.(*provider.OpenAICompatibility); isOk {
					// 设置基础 URL 和是否直接使用基础 URL
					instance.SetBaseUrl(model.BaseURL, model.BaseURLDirect)
				}
			}

			// 记录使用开始时间
			startTime := time.Now()

			// 是否是流式传输
			isStream := false
			// 获取 stream 字段
			streamResult := gjson.GetBytes(rawJson, "stream")
			// 如果 stream 字段为 true，则设置为流式传输
			if streamResult.Type == gjson.True {
				isStream = true
			}

			// 设置模型名称为提供者模型名称
			rawJson, _ = sjson.SetBytes(rawJson, "model", model.ProviderModelName)

			// 如果模型的输入或输出价格不为0，则检查用户余额是否充足
			if model.InputPricePerToken > 0 || model.OutputPricePerToken > 0 {
				// 获取用户信息
				var user models.User
				if err = db.First(&user, apiKey.UserID).Error; err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get user information: %v", err), "code": 500})
					return
				}

				// 如果是管理员，则不检查余额
				if !user.IsAdmin {
					// 估算使用的令牌数（这里只是一个粗略估计，实际使用可能会更多）
					// 从请求中获取消息
					messagesResult := gjson.GetBytes(rawJson, "messages")
					estimatedPromptTokens := 0
					estimatedOutputTokens := 0

					// 粗略估计输入令牌数（每个字符约为0.25个令牌）
					if messagesResult.IsArray() {
						messagesResult.ForEach(func(_, value gjson.Result) bool {
							content := value.Get("content").String()
							estimatedPromptTokens += len(content) / 4
							return true
						})
					}

					// 粗略估计输出令牌数（假设输出为输入的两倍）
					estimatedOutputTokens = estimatedPromptTokens * 2

					// 计算估计成本
					estimatedInputCost := float64(estimatedPromptTokens) * model.InputPricePerToken
					estimatedOutputCost := float64(estimatedOutputTokens) * model.OutputPricePerToken
					estimatedTotalCost := estimatedInputCost + estimatedOutputCost

					// 检查用户余额是否充足
					if user.Credits < estimatedTotalCost {
						c.JSON(http.StatusPaymentRequired, gin.H{"error": "Insufficient credits", "code": 402})
						return
					}
				}

			}

			// 如果模型名称包含 gemini，则检查 reasoning_effort 字段
			if model.SupportGoogleThinking {
				reasoningEffortResult := gjson.GetBytes(rawJson, "reasoning_effort")
				if reasoningEffortResult.Type == gjson.String {
					if reasoningEffortResult.String() == "none" {
						rawJson, err = sjson.DeleteBytes(rawJson, "reasoning_effort")
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
						rawJson, err = sjson.SetBytes(rawJson, "extra_body.google.thinking_config.thinking_budget", 0)
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
						rawJson, err = sjson.SetBytes(rawJson, "extra_body.google.thinking_config.include_thoughts", false)
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
					} else if reasoningEffortResult.String() == "auto" {
						rawJson, err = sjson.DeleteBytes(rawJson, "reasoning_effort")
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
						rawJson, err = sjson.SetBytes(rawJson, "extra_body.google.thinking_config.thinking_budget", -1)
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
						rawJson, err = sjson.SetBytes(rawJson, "extra_body.google.thinking_config.include_thoughts", true)
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
					} else if reasoningEffortResult.String() == "low" {
						rawJson, err = sjson.DeleteBytes(rawJson, "reasoning_effort")
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
						rawJson, err = sjson.SetBytes(rawJson, "extra_body.google.thinking_config.thinking_budget", 1024)
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
						rawJson, err = sjson.SetBytes(rawJson, "extra_body.google.thinking_config.include_thoughts", true)
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
					} else if reasoningEffortResult.String() == "medium" {
						rawJson, err = sjson.DeleteBytes(rawJson, "reasoning_effort")
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
						rawJson, err = sjson.SetBytes(rawJson, "extra_body.google.thinking_config.thinking_budget", 8192)
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
						rawJson, err = sjson.SetBytes(rawJson, "extra_body.google.thinking_config.include_thoughts", true)
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
					} else if reasoningEffortResult.String() == "high" {
						rawJson, err = sjson.DeleteBytes(rawJson, "reasoning_effort")
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
						rawJson, err = sjson.SetBytes(rawJson, "extra_body.google.thinking_config.thinking_budget", 24576)
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
						rawJson, err = sjson.SetBytes(rawJson, "extra_body.google.thinking_config.include_thoughts", true)
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
					} else {
						rawJson, err = sjson.DeleteBytes(rawJson, "reasoning_effort")
						if err != nil {
							c.JSON(http.StatusInternalServerError, gin.H{"error": "request body error", "code": 500})
							return
						}
					}
				}
			}

			// 检查是否请求流式传输
			if isStream {
				// 处理流式聊天补全
				err = handleStreamingChatCompletion(c, providerInstance, rawJson, model, apiKey, db, startTime)
			} else {
				// 删除 stream_options 标签
				rawJson, _ = sjson.DeleteBytes(rawJson, "stream_options")
				// 处理非流式请求
				err = handleNonStreamingChatCompletion(c, providerInstance, rawJson, model, apiKey, db, startTime)
			}

			// 关闭提供者实例
			_ = providerInstance.Close()

			// 如果没有错误
			if err == nil {
				log.Printf("Request provider %s's model %s OK\n", model.Provider.Name, model.ProviderModelName)
				// 跳出循环
				break
			} else {
				log.Printf("Request provide %s's model %s error: %s\n", model.Provider.Name, model.ProviderModelName, err.Error())
			}
		}

		// 如果有错误
		if err != nil {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error": gin.H{
					"message": "All providers response error.",
					"code":    503,
				},
			})
		}
	}
}

// / handleStreamingChatCompletion 处理流式聊天补全请求
// / c: gin 上下文
// / p: 提供者实例
// / request: 请求数据
// / model: 模型
// / apiKey: API 密钥
// / db: 数据库连接
// / startTime: 请求开始时间
func handleStreamingChatCompletion(c *gin.Context, p provider.Provider, request []byte, model models.Model, apiKey models.APIKey, db *gorm.DB, startTime time.Time) error {
	// 设置流式传输的响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")

	// 创建一个带有超时的上下文
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Minute)
	defer cancel()

	// 调用提供者的流式 API
	usage := provider.Usage{
		PromptTokens:     0,
		CompletionTokens: 0,
		TotalTokens:      0,
		PromptTokensDetails: provider.UsagePromptTokensDetails{
			CachedTokens: 0,
			AudioTokens:  0,
		},
		CompletionTokensDetails: provider.UsageCompletionTokensDetails{
			AudioTokens:     0,
			ReasoningTokens: 0,
		},
	}

	// 获取流式响应
	stream, err, errBody := p.CreateChatCompletionStream(ctx, cancel, request, model, &usage)
	// 如果获取流式响应失败
	if err != nil {
		// 如果有错误体
		if errBody != nil {
			// 将错误体写入响应
			_, err = c.Writer.Write(errBody)
		}
		// c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to create chat completion stream: %v", err)})
		return err
	}
	// 延迟关闭流
	defer func() {
		err = stream.Close()
	}()

	// 使用 channel 接收读取结果
	resultChan := make(chan []byte)
	resultEOFChan := make(chan bool)
	errChan := make(chan error)

	// 在 goroutine 中读取数据
	go func() {
		for {
			// 从流中读取数据
			buffer := make([]byte, 4096)
			var n int
			n, err = stream.Read(buffer)
			// 如果读取到 EOF
			if err == io.EOF {
				// 发送 EOF 信号
				resultEOFChan <- true
				return
			}
			// 如果读取出错
			if err != nil {
				// 发送错误信号
				errChan <- err
				return
			}

			// 发送读取到的数据
			resultChan <- buffer[:n]
		}
	}()

	// 错误返回值
	var requestError error
	// 处理中的标签
	processingTag := []byte(": ROUTER-FOR-ME PROCESSING\n\n")
	// 将响应流式传输到客户端

	startGoogleThinking := false
	c.Stream(func(w io.Writer) bool {
		for {
			select {
			// 如果接收到数据
			case response := <-resultChan:
				// 去除首尾空格
				tmp := bytes.TrimSpace(response)
				// 如果不是以 : 或 data: 开头
				if !bytes.HasPrefix(tmp, _const.TagNoData) && !bytes.HasPrefix(tmp, _const.TagData) {
					// 设置错误信息
					requestError = fmt.Errorf("unexpected response: %s", string(tmp))
					return false // stop c.Stream
				} else {
					if model.SupportGoogleThinking {
						response = bytes.TrimSpace(response[5:])
						thoughtResult := gjson.GetBytes(response, "choices.0.delta.extra_content.google.thought")
						if thoughtResult.Type == gjson.True {
							if !startGoogleThinking {
								startGoogleThinking = true
							}
							contentResult := gjson.GetBytes(response, "choices.0.delta.content")
							if contentResult.Type == gjson.String {
								reasoningContent := contentResult.String()
								if strings.HasPrefix(reasoningContent, "<thought>") {
									reasoningContent = reasoningContent[9:]
								}
								response, _ = sjson.SetBytes(response, "choices.0.delta.reasoning_content", reasoningContent)
								response, _ = sjson.DeleteBytes(response, "choices.0.delta.content")
								response, _ = sjson.DeleteBytes(response, "choices.0.delta.extra_content")
							}
						} else if startGoogleThinking {
							contentResult := gjson.GetBytes(response, "choices.0.delta.content")
							content := contentResult.String()
							if strings.HasPrefix(content, "</thought>") {
								content = content[10:]
								response, _ = sjson.SetBytes(response, "choices.0.delta.content", content)
								startGoogleThinking = false
							}
						}
						_, err = w.Write([]byte("data: "))
						_, err = w.Write(response)
						_, err = w.Write([]byte("\n\n"))
					} else {
						// 写入响应
						_, err = w.Write(response)
					}
					return true // continue c.Stream
				}
			// 如果接收到错误
			case err = <-errChan:
				// c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to create chat completion: %v", err)})
				// 设置错误信息
				requestError = err
				return false // stop c.Stream
			// 如果接收到 EOF
			case <-resultEOFChan:
				return false // stop c.Stream
			// 如果超时
			case <-time.After(500 * time.Millisecond):
				// 写入处理中标签
				_, err = w.Write(processingTag)
				return true // continue c.Stream
			}
		}
	})

	// 流式传输完成后记录使用情况
	if requestError == nil {
		recordUsage(db, apiKey, model, request, usage, startTime)
	}

	return requestError
}

// / handleNonStreamingChatCompletion 处理非流式聊天补全请求
// / c: gin 上下文
// / p: 提供者实例
// / request: 请求数据
// / model: 模型
// / apiKey: API 密钥
// / db: 数据库连接
// / startTime: 请求开始时间
func handleNonStreamingChatCompletion(c *gin.Context, p provider.Provider, request []byte, model models.Model, apiKey models.APIKey, db *gorm.DB, startTime time.Time) error {
	// 创建一个带有超时的上下文
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Minute)
	defer cancel()

	// 调用提供者的 API
	usage := provider.Usage{
		PromptTokens:     0,
		CompletionTokens: 0,
		TotalTokens:      0,
		PromptTokensDetails: provider.UsagePromptTokensDetails{
			CachedTokens: 0,
			AudioTokens:  0,
		},
		CompletionTokensDetails: provider.UsageCompletionTokensDetails{
			AudioTokens:     0,
			ReasoningTokens: 0,
		},
	}

	// 定义错误响应结构体
	type ErrorResponse struct {
		Error error
		Body  []byte
	}

	var err error
	// 使用 channel 接收读取结果
	resultChan := make(chan []byte)
	errChan := make(chan ErrorResponse)

	// 在 goroutine 中读取数据
	go func() {
		var response []byte
		var errBody []byte
		// 调用提供者的 CreateChatCompletion 方法
		response, err, errBody = p.CreateChatCompletion(ctx, cancel, request, model, &usage)
		// 如果有错误
		if err != nil {
			// 发送错误响应
			errChan <- ErrorResponse{Error: err, Body: errBody}
			return
		}
		// 发送响应
		resultChan <- response
	}()

	// 错误返回值
	var requestError error

	// 将响应流式传输到客户端
	c.Stream(func(w io.Writer) bool {
		for {
			select {
			// 如果接收到数据
			case response := <-resultChan:
				// 返回响应
				c.Header("Content-Type", "application/json; charset=utf-8")
				c.Writer.WriteHeader(http.StatusOK)
				response = bytes.TrimSpace(response)

				if model.SupportGoogleThinking {
					thoughtResult := gjson.GetBytes(response, "choices.0.message.extra_content.google.thought")
					if thoughtResult.Type == gjson.True {
						contentResult := gjson.GetBytes(response, "choices.0.message.content")
						if contentResult.Type == gjson.String {
							content := contentResult.String()
							thoughtStartIndex := strings.Index(content, "<thought>")
							thoughtEndIndex := strings.Index(content, "</thought>")
							if thoughtStartIndex != -1 && thoughtEndIndex != -1 {
								response, _ = sjson.SetBytes(response, "choices.0.message.reasoning_content", content[thoughtStartIndex+9:thoughtEndIndex])
								response, _ = sjson.SetBytes(response, "choices.0.message.content", content[:thoughtStartIndex]+content[thoughtEndIndex+10:])
								response, _ = sjson.DeleteBytes(response, "choices.0.message.extra_content")
							}
						}
					}
				}

				_, err = w.Write(response)
				return false // stop c.Stream
			// 如果接收到错误
			case responseError := <-errChan:
				// c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to create chat completion: %v", err)})
				// 设置错误信息
				requestError = responseError.Error
				// 写入错误体
				_, err = c.Writer.Write(responseError.Body)
				return false // stop c.Stream
			// 如果超时
			case <-time.After(500 * time.Millisecond):
				// 写入换行符
				_, err = w.Write([]byte{10})
				return true // continue c.Stream
			}
		}
	})

	// 记录使用情况
	if requestError == nil {
		recordUsage(db, apiKey, model, request, usage, startTime)
	}

	return requestError
}

// / recordUsage 记录 API 使用情况，用于计费和速率限制
// / db: 数据库连接
// / apiKey: API 密钥
// / model: 模型
// / request: 请求数据
// / usage: 使用情况
// / startTime: 请求开始时间
func recordUsage(db *gorm.DB, apiKey models.APIKey, model models.Model, request []byte, usage provider.Usage, startTime time.Time) {
	// 从请求中获取模型 ID
	// 在实际实现中，您需要在数据库中查找模型 ID
	// 现在，我们只使用一个占位符值
	var modelID = model.ID

	// 使用计费包记录使用情况
	billing.RecordChatUsage(apiKey, request, usage, startTime, modelID)

	// 为了向后兼容，也直接保存到数据库
	// 一旦计费队列完全测试完毕，就可以删除此部分
	// 计算处理时间
	processingMs := int(time.Since(startTime).Milliseconds())

	// 创建使用记录
	u := models.Usage{
		UserID:       apiKey.UserID,
		APIKeyID:     apiKey.ID,
		RequestType:  "chat",
		ProcessingMs: processingMs,
		StatusCode:   http.StatusOK,
		ModelID:      modelID,
	}

	// 如果有响应，则添加令牌计数
	u.PromptTokens = usage.PromptTokens
	u.OutputTokens = usage.CompletionTokens
	u.TotalTokens = usage.TotalTokens

	// 保存使用记录
	db.Create(&u)

	// 在 Redis 中记录令牌使用情况
	redisRateLimiterObj := redisRateLimiter
	if redisRateLimiterObj != nil {
		// 创建一个带有超时的上下文
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// 增加令牌计数
		totalTokens := usage.PromptTokens + usage.CompletionTokens
		err := redisRateLimiterObj.IncrementTokenCount(ctx, apiKey.Key, totalTokens)
		if err != nil {
			log.Printf("Warning: Failed to increment token count in Redis: %v", err)
		}
	}

	// 如果模型的输入或输出价格不为0，则扣除用户余额
	if model.InputPricePerToken > 0 || model.OutputPricePerToken > 0 {
		// 计算成本
		inputCost := float64(usage.PromptTokens) * model.InputPricePerToken
		outputCost := float64(usage.CompletionTokens) * model.OutputPricePerToken
		totalCost := inputCost + outputCost

		// 更新用户余额
		var user models.User
		if err := db.First(&user, apiKey.UserID).Error; err == nil {
			// 扣除余额
			db.Model(&user).Update("credits", user.Credits-totalCost)
		} else {
			log.Printf("Warning: Failed to update user credits: %v", err)
		}
	}
}
