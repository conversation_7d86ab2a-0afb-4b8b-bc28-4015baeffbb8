package api

import (
	"context"
	"encoding/json"
	"fmt"
	_const "github.com/luispater/ai-router/const"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/luispater/ai-router/billing"
	"github.com/luispater/ai-router/models"
	"github.com/luispater/ai-router/provider"
	"gorm.io/gorm"
)

// FileUploadHandler handles file upload requests
func FileUploadHandler(db *gorm.DB, providerRegistry map[_const.ProviderType]provider.ProviderFactory) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the API key from the context
		apiKeyObj, exists := c.Get("apiKey")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "API key not found in context"})
			return
		}
		apiKey := apiKeyObj.(models.APIKey)

		// Get the file
		file, err := c.FormFile("file")
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to get file: %v", err)})
			return
		}

		// Get the purpose
		purpose := c.DefaultPostForm("purpose", "fine-tune")

		// Get the provider type from the API key
		providerObj, exists := c.Get("provider")
		if !exists {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Provider not found in context"})
			return
		}
		providerModel := providerObj.(models.Provider)

		// Determine the provider type
		var providerType _const.ProviderType
		switch providerModel.Name {
		case "gemini":
			providerType = _const.ProviderGemini
		default:
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Unsupported provider: %s", providerModel.Name)})
			return
		}

		// Get the provider factory
		factory, ok := providerRegistry[providerType]
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Provider factory not found for: %d", providerType)})
			return
		}

		// Create provider options
		options := map[string]interface{}{}
		if providerModel.Config != "" {
			if err = json.Unmarshal([]byte(providerModel.Config), &options); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to parse provider config: %v", err)})
				return
			}
		}

		// Get a default model for the provider
		var model models.Model
		if err = db.Where("provider_id = ?", providerModel.ID).First(&model).Error; err != nil {
			// If model not found, use default API key
			log.Printf("No model found for provider %s, using default API key", providerModel.Name)
		}

		// Create the provider instance
		providerInstance, err := factory(db)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to create provider: %v", err)})
			return
		}
		defer func() {
			err = providerInstance.Close()
		}()

		// Record the start time for usage tracking
		startTime := time.Now()

		// Create a context with timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Minute) // Longer timeout for file uploads
		defer cancel()

		// Process the file
		fileID, err := providerInstance.ProcessFile(ctx, file, purpose)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to process file: %v", err)})
			return
		}

		// Record usage
		recordFileUsage(db, apiKey, purpose, startTime)

		// Return the response
		c.JSON(http.StatusOK, gin.H{
			"id":             fileID,
			"object":         "file",
			"purpose":        purpose,
			"filename":       file.Filename,
			"bytes":          file.Size,
			"created_at":     time.Now().Unix(),
			"status":         "processed",
			"status_details": nil,
		})
	}
}

// recordFileUsage records API usage for file uploads
func recordFileUsage(db *gorm.DB, apiKey models.APIKey, purpose string, startTime time.Time) {
	// Get the model ID from the request
	// In a real implementation, you would look up the model ID in the database
	// For now, we'll just use a placeholder value
	var modelID uint = 1

	// Use the billing package to record usage
	billing.RecordFileUsage(apiKey, purpose, startTime, modelID)

	// For backward compatibility, also save to the database directly
	// This can be removed once the billing queue is fully tested
	// Calculate processing time
	processingMs := int(time.Since(startTime).Milliseconds())

	// Create usage record
	usage := models.Usage{
		UserID:       apiKey.UserID,
		APIKeyID:     apiKey.ID,
		RequestType:  "file_upload",
		ProcessingMs: processingMs,
		StatusCode:   http.StatusOK,
		ModelID:      modelID,
	}

	// Save usage record
	db.Create(&usage)
}

// FileListHandler handles requests to list files
func FileListHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// In a real implementation, this would query the database for files
		// For now, we'll just return an empty list
		c.JSON(http.StatusOK, gin.H{
			"object":   "list",
			"data":     []interface{}{},
			"has_more": false,
		})
	}
}

// FileDeleteHandler handles requests to delete files
func FileDeleteHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the file ID from the URL
		fileID := c.Param("file_id")
		if fileID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "File ID is required"})
			return
		}

		// In a real implementation, this would delete the file from storage
		// For now, we'll just return a success response
		c.JSON(http.StatusOK, gin.H{
			"id":      fileID,
			"object":  "file",
			"deleted": true,
		})
	}
}

// FileRetrieveHandler handles requests to retrieve file information
func FileRetrieveHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the file ID from the URL
		fileID := c.Param("file_id")
		if fileID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "File ID is required"})
			return
		}

		// In a real implementation, this would query the database for the file
		// For now, we'll just return a placeholder response
		c.JSON(http.StatusOK, gin.H{
			"id":             fileID,
			"object":         "file",
			"purpose":        "fine-tune",
			"filename":       "example.json",
			"bytes":          1024,
			"created_at":     time.Now().Unix(),
			"status":         "processed",
			"status_details": nil,
		})
	}
}

// FileContentHandler handles requests to retrieve file content
func FileContentHandler(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the file ID from the URL
		fileID := c.Param("file_id")
		if fileID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "File ID is required"})
			return
		}

		// In a real implementation, this would retrieve the file content from storage
		// For now, we'll just return a placeholder response
		c.String(http.StatusOK, "Example file content")
	}
}
