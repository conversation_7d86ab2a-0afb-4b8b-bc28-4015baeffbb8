package api

import (
	"context"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/luispater/ai-router/core"
	"github.com/luispater/ai-router/models"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// AuthMiddleware authenticates requests using API keys
func AuthMiddleware(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": "Missing API key",
			})
			return
		}

		// Extract the API key
		parts := strings.Split(authHeader, " ")
		var apiKey string
		if len(parts) == 2 && strings.ToLower(parts[0]) == "bearer" {
			apiKey = parts[1]
		} else {
			apiKey = authHeader
		}

		// Find the API key in the database
		var key models.APIKey
		result := db.Preload("User").Where("key = ? AND is_active = ?", apiKey, true).First(&key)
		if result.Error != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": "Invalid API key",
			})
			return
		}

		// Check if the API key has expired
		if !key.ExpiresAt.IsZero() && key.ExpiresAt.Before(time.Now()) {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error": "API key has expired",
			})
			return
		}

		// Store the API key and user in the context
		c.Set("apiKey", key)
		c.Set("user", key.User)

		c.Next()
	}
}

// Global Redis rate limiter instance
var redisRateLimiter *core.RedisRateLimiter

// InitRedisRateLimiter initializes the Redis rate limiter
func InitRedisRateLimiter(client *redis.Client) {
	redisRateLimiter = core.NewRedisRateLimiter(client)
}

// RedisRateLimitMiddleware applies rate limiting based on API key settings using Redis
func RedisRateLimitMiddleware(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if Redis rate limiter is initialized
		if redisRateLimiter == nil {
			log.Println("Warning: Redis rate limiter not initialized, skipping rate limiting")
			c.Next()
			return
		}

		// Get the API key from the context
		apiKeyObj, exists := c.Get("apiKey")
		if !exists {
			c.Next()
			return
		}

		key := apiKeyObj.(models.APIKey)

		// Create a context with timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
		defer cancel()

		// Check rate limits
		limits := map[string]int{
			"rps": key.RPS,
			"tps": key.TPS,
			"rpm": key.RPM,
			"tpm": key.TPM,
			"rph": key.RPH,
			"tph": key.TPH,
			"rpd": key.RPD,
			"tpd": key.TPD,
		}

		allowed, limitValues, remainingValues, err := redisRateLimiter.CheckRateLimits(ctx, key.Key, limits)
		if err != nil {
			log.Printf("Warning: Failed to check rate limits: %v", err)
			c.Next()
			return
		}

		// Add rate limit headers
		addRateLimitHeaders(c, limitValues, remainingValues)

		if !allowed {
			c.AbortWithStatusJSON(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded",
			})
			return
		}

		// Increment request count in Redis
		err = redisRateLimiter.IncrementRequestCount(ctx, key.Key)
		if err != nil {
			log.Printf("Warning: Failed to increment request count: %v", err)
		}

		// Store the rate limiter in the context for token counting later
		c.Set("redisRateLimiter", redisRateLimiter)

		c.Next()
	}
}

// addRateLimitHeaders adds rate limit headers to the response
func addRateLimitHeaders(c *gin.Context, limits, remaining map[string]int) {
	// Add limit headers
	if val, ok := limits["rps"]; ok && val > 0 {
		c.Header("X-RPS-Limit", strconv.Itoa(val))
	} else {
		c.Header("X-RPS-Limit", "unlimited")
	}

	if val, ok := limits["tps"]; ok && val > 0 {
		c.Header("X-TPS-Limit", strconv.Itoa(val))
	} else {
		c.Header("X-TPS-Limit", "unlimited")
	}

	if val, ok := limits["rpm"]; ok && val > 0 {
		c.Header("X-RPM-Limit", strconv.Itoa(val))
	} else {
		c.Header("X-RPM-Limit", "unlimited")
	}

	if val, ok := limits["tpm"]; ok && val > 0 {
		c.Header("X-TPM-Limit", strconv.Itoa(val))
	} else {
		c.Header("X-TPM-Limit", "unlimited")
	}

	if val, ok := limits["rph"]; ok && val > 0 {
		c.Header("X-RPH-Limit", strconv.Itoa(val))
	} else {
		c.Header("X-RPH-Limit", "unlimited")
	}

	if val, ok := limits["tph"]; ok && val > 0 {
		c.Header("X-TPH-Limit", strconv.Itoa(val))
	} else {
		c.Header("X-TPH-Limit", "unlimited")
	}

	if val, ok := limits["rpd"]; ok && val > 0 {
		c.Header("X-RPD-Limit", strconv.Itoa(val))
	} else {
		c.Header("X-RPD-Limit", "unlimited")
	}

	if val, ok := limits["tpd"]; ok && val > 0 {
		c.Header("X-TPD-Limit", strconv.Itoa(val))
	} else {
		c.Header("X-TPD-Limit", "unlimited")
	}

	// Add remaining headers
	if val, ok := remaining["rps"]; ok {
		c.Header("X-RPS-Remain", strconv.Itoa(val))
	} else {
		if valLimit, okLimit := limits["rps"]; okLimit && valLimit > 0 {
			c.Header("X-RPS-Remain", strconv.Itoa(valLimit))
		} else {
			c.Header("X-RPS-Remain", "infinite")
		}
	}

	if val, ok := remaining["tps"]; ok {
		c.Header("X-TPS-Remain", strconv.Itoa(val))
	} else {
		if valLimit, okLimit := limits["tps"]; okLimit && valLimit > 0 {
			c.Header("X-TPS-Remain", strconv.Itoa(valLimit))
		} else {
			c.Header("X-TPS-Remain", "infinite")
		}
	}

	if val, ok := remaining["rpm"]; ok {
		c.Header("X-RPM-Remain", strconv.Itoa(val))
	} else {
		if valLimit, okLimit := limits["rpm"]; okLimit && valLimit > 0 {
			c.Header("X-RPM-Remain", strconv.Itoa(valLimit))
		} else {
			c.Header("X-RPM-Remain", "infinite")
		}
	}

	if val, ok := remaining["tpm"]; ok {
		c.Header("X-TPM-Remain", strconv.Itoa(val))
	} else {
		if valLimit, okLimit := limits["tpm"]; okLimit && valLimit > 0 {
			c.Header("X-TPM-Remain", strconv.Itoa(valLimit))
		} else {
			c.Header("X-TPM-Remain", "infinite")
		}
	}

	if val, ok := remaining["rph"]; ok {
		c.Header("X-RPH-Remain", strconv.Itoa(val))
	} else {
		if valLimit, okLimit := limits["rph"]; okLimit && valLimit > 0 {
			c.Header("X-RPH-Remain", strconv.Itoa(valLimit))
		} else {
			c.Header("X-RPH-Remain", "infinite")
		}
	}

	if val, ok := remaining["tph"]; ok {
		c.Header("X-TPH-Remain", strconv.Itoa(val))
	} else {
		if valLimit, okLimit := limits["tph"]; okLimit && valLimit > 0 {
			c.Header("X-TPH-Remain", strconv.Itoa(valLimit))
		} else {
			c.Header("X-TPH-Remain", "infinite")
		}
	}

	if val, ok := remaining["rpd"]; ok {
		c.Header("X-RPD-Remain", strconv.Itoa(val))
	} else {
		if valLimit, okLimit := limits["rpd"]; okLimit && valLimit > 0 {
			c.Header("X-RPD-Remain", strconv.Itoa(valLimit))
		} else {
			c.Header("X-RPD-Remain", "infinite")
		}
	}

	if val, ok := remaining["tpd"]; ok {
		c.Header("X-TPD-Remain", strconv.Itoa(val))
	} else {
		if valLimit, okLimit := limits["tpd"]; okLimit && valLimit > 0 {
			c.Header("X-TPD-Remain", strconv.Itoa(valLimit))
		} else {
			c.Header("X-TPD-Remain", "infinite")
		}
	}
}

// RateLimitMiddleware applies rate limiting based on API key settings
func RateLimitMiddleware(rateLimiters map[uint]*core.RateLimiter, db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the API key from the context
		apiKey, exists := c.Get("apiKey")
		if !exists {
			c.Next()
			return
		}

		key := apiKey.(models.APIKey)

		// Get or create a rate limiter for this API key
		limiter, exists := rateLimiters[key.ID]
		if !exists {
			// Determine rate limit (RPM)
			rpm := key.RPM
			if rpm == 0 {
				// If not set on the API key, get the model's default RPM
				// This is a simplification - in a real app, you'd determine the model from the request
				// var model models.Model
				// db.Where("provider_id = ?", key.ProviderID).First(&model)
				// rpm = model.RPM
			}

			// Create a new rate limiter
			limiter = core.NewRateLimiter(rpm, time.Minute)
			rateLimiters[key.ID] = limiter
		}

		// Create a context with timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
		defer cancel()

		// Try to execute within rate limits
		err := limiter.Execute(func() {
			// This function will be executed when a rate limit token is available
			c.Next()
		}, ctx)

		if err != nil {
			// If we get here, rate limiting failed (timeout or limiter closed)
			c.AbortWithStatusJSON(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded",
			})
			return
		}
	}
}

// ErrorMiddleware handles errors and provides consistent error responses
func ErrorMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// Check if there were any errors during the request
		if len(c.Errors) > 0 {
			// Get the last error
			err := c.Errors.Last()

			// Determine the status code
			statusCode := http.StatusInternalServerError
			if c.Writer.Status() != http.StatusOK {
				statusCode = c.Writer.Status()
			}

			// Send a JSON response with the error
			c.JSON(statusCode, gin.H{
				"error": err.Error(),
			})
		}
	}
}

// CORSMiddleware adds CORS headers to responses
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// LoggingMiddleware logs requests and responses
func LoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Start timer
		start := time.Now()

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)

		// Log request details
		statusCode := c.Writer.Status()

		// Log using Gin's logger
		if statusCode >= 400 {
			// Log errors with more details
			// err := c.Error(fmt.Errorf("status error")).SetMeta(gin.H{
			// 	"latency":   latency,
			// 	"status":    statusCode,
			// 	"method":    c.Request.Method,
			// 	"path":      c.Request.URL.Path,
			// 	"client_ip": c.ClientIP(),
			// })
			// if err != nil {
			// 	log.Println(err.Error())
			// }
			c.Set("latency", latency)
		} else {
			// Log successful requests
			c.Set("latency", latency)
		}
	}
}
