package api

import (
	"context"
	"encoding/json"
	"fmt"
	_const "github.com/luispater/ai-router/const"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/luispater/ai-router/billing"
	"github.com/luispater/ai-router/models"
	"github.com/luispater/ai-router/provider"
	"gorm.io/gorm"
)

// ImageGenerationHandler handles image generation requests
func ImageGenerationHandler(db *gorm.DB, providerRegistry map[_const.ProviderType]provider.ProviderFactory) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the API key from the context
		apiKeyObj, exists := c.Get("apiKey")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "API key not found in context"})
			return
		}
		apiKey := apiKeyObj.(models.APIKey)

		// Parse the request
		var request provider.ImageGenerationRequest
		if err := c.ShouldBindJSON(&request); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid request: %v", err)})
			return
		}

		var providerModels []models.Model
		// 预加载 Provider 关联，并根据模型名称和启用状态查找模型，并根据输出和输入价格排序
		if err := db.Preload("Provider").Where("name = ? AND enabled = true", request.Model).Order("output_price_per_token ASC, input_price_per_token ASC").Find(&providerModels).Error; err != nil || len(providerModels) == 0 {
			// 如果模型未找到，则使用默认模型
			log.Printf("Model %s not found", request.Model)
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Model %s not found", request.Model), "code": 500})
			return
		}

		for _, model := range providerModels {
			providerType := _const.ProviderType(model.ProviderID)

			// Get the provider factory
			factory, ok := providerRegistry[providerType]
			if !ok {
				c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Provider factory not found for: %d", providerType)})
				return
			}

			// Get the model from the database
			// Create the provider instance
			providerInstance, err := factory(db)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to create provider: %v", err)})
				return
			}

			// 如果模型的输出价格不为0，则检查用户余额是否充足
			if model.OutputPricePerToken > 0 {
				// 获取用户信息
				var user models.User
				if err = db.First(&user, apiKey.UserID).Error; err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to get user information: %v", err), "code": 500})
					return
				}

				// 如果是管理员，则不检查余额
				if !user.IsAdmin {
					// 图像生成每次计为1个输出令牌
					estimatedCost := model.OutputPricePerToken

					// 检查用户余额是否充足
					if user.Credits < estimatedCost {
						c.JSON(http.StatusPaymentRequired, gin.H{"error": "Insufficient credits", "code": 402})
						return
					}
				}
			}

			// Record the start time for usage tracking
			startTime := time.Now()

			// Create a context with timeout
			ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Minute)

			// Call the provider's API
			response, err, _ := providerInstance.CreateImage(ctx, model, request)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate image: %v", err)})
				cancel()
				return
			}

			// Record usage
			recordImageUsage(db, apiKey, model, request, startTime)

			// Return the response
			c.JSON(http.StatusOK, response)
			cancel()
			return
		}

		// 如果有错误
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": gin.H{
				"message": "All providers response error.",
				"code":    503,
			},
		})
	}
}

// recordImageUsage records API usage for image generation
func recordImageUsage(db *gorm.DB, apiKey models.APIKey, model models.Model, request provider.ImageGenerationRequest, startTime time.Time) {
	// Get the model ID from the request
	// In a real implementation, you would look up the model ID in the database
	// For now, we'll just use a placeholder value

	// Use the billing package to record usage
	billing.RecordImageUsage(apiKey, "image_generation", startTime, model.ID)

	// For backward compatibility, also save to the database directly
	// This can be removed once the billing queue is fully tested
	// Calculate processing time
	processingMs := int(time.Since(startTime).Milliseconds())

	// Create usage record
	usage := models.Usage{
		UserID:       apiKey.UserID,
		APIKeyID:     apiKey.ID,
		RequestType:  "image_generation",
		ProcessingMs: processingMs,
		StatusCode:   http.StatusOK,
		ModelID:      model.ID,
		// For image generation, we count as 1 output token
		OutputTokens: 1,
		TotalTokens:  1,
	}

	// Save usage record
	db.Create(&usage)

	// Increment request count in Redis
	redisRateLimiterObj := redisRateLimiter
	if redisRateLimiterObj != nil {
		// Create a context with timeout
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// Increment request count
		err := redisRateLimiterObj.IncrementRequestCount(ctx, apiKey.Key)
		if err != nil {
			log.Printf("Warning: Failed to increment request count in Redis: %v", err)
		}
	}

	// If the model has a non-zero output price, deduct from user credits
	if model.OutputPricePerToken > 0 {
		// Calculate cost (1 token for image generation)
		totalCost := model.OutputPricePerToken

		// Update user balance
		var user models.User
		if err := db.First(&user, apiKey.UserID).Error; err == nil {
			// Deduct credits
			db.Model(&user).Update("credits", user.Credits-totalCost)
		} else {
			log.Printf("Warning: Failed to update user credits: %v", err)
		}
	}
}

// ImageVariationHandler handles image variation requests
func ImageVariationHandler(db *gorm.DB, providerRegistry map[_const.ProviderType]provider.ProviderFactory) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get the API key from the context
		apiKeyObj, exists := c.Get("apiKey")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "API key not found in context"})
			return
		}
		apiKey := apiKeyObj.(models.APIKey)

		// Get the image file
		file, err := c.FormFile("image")
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Failed to get image file: %v", err)})
			return
		}

		// Parse other form parameters
		nStr := c.DefaultPostForm("n", "1")
		size := c.DefaultPostForm("size", "1024x1024")
		responseFormat := c.DefaultPostForm("response_format", "url")
		user := c.PostForm("user")

		// Convert n to int (not used in this implementation but would be in a real one)
		n := 1
		// We're ignoring the error here since we have a default value
		_, err = fmt.Sscanf(nStr, "%d", &n)

		// Get the provider type from the API key
		providerObj, exists := c.Get("provider")
		if !exists {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Provider not found in context"})
			return
		}
		providerModel := providerObj.(models.Provider)

		// Currently, only OpenAI supports image variations
		if providerModel.Name != "openai" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Image variations are only supported by OpenAI"})
			return
		}

		// Get the provider factory
		factory, ok := providerRegistry[_const.ProviderGemini]
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Provider factory not found for OpenAI"})
			return
		}

		// Create provider options
		options := map[string]interface{}{}
		if providerModel.Config != "" {
			if err = json.Unmarshal([]byte(providerModel.Config), &options); err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to parse provider config: %v", err)})
				return
			}
		}

		// Get a default model for the provider
		var model models.Model
		if err = db.Where("provider_id = ?", providerModel.ID).First(&model).Error; err != nil {
			// If model not found, use default API key
			log.Printf("No model found for provider %s", providerModel.Name)
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("No model found for provider %s", providerModel.Name)})
			return
		}

		// Create the provider instance
		providerInstance, err := factory(db)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to create provider: %v", err)})
			return
		}
		defer func() {
			err = providerInstance.Close()
		}()

		// Record the start time for usage tracking
		startTime := time.Now()

		// Create a context with timeout
		ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Minute)
		defer cancel()

		// Process the file
		fileID, err := providerInstance.ProcessFile(ctx, file, "image_variation")
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to process image file: %v", err)})
			return
		}

		// For now, we'll just return a placeholder response
		// In a real implementation, you would call the provider's API to generate variations
		response := provider.ImageResponse{
			Created: time.Now().Unix(),
			Data: []provider.ImageObject{
				{
					URL: fmt.Sprintf("https://example.com/images/%s", fileID),
				},
			},
		}

		// Record usage
		recordImageUsage(db, apiKey, model, provider.ImageGenerationRequest{
			N:              1,
			Size:           size,
			ResponseFormat: responseFormat,
			User:           user,
		}, startTime)

		// Return the response
		c.JSON(http.StatusOK, response)
	}
}
