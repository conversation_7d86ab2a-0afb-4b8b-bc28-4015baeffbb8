# AI Router Development Guidelines

This document provides guidelines and information for developers working on the AI Router project.

## Build and Configuration Instructions

### Prerequisites
- Go 1.23 or later (toolchain 1.24.0 recommended)
- PostgreSQL database
- Redis (optional, for billing queue)

### Setup and Build

1. **Clone the repository**
   ```bash
   git clone https://github.com/luispater/ai-router.git
   cd ai-router
   ```

2. **Install dependencies**
   ```bash
   go mod download
   ```

3. **Configure the application**
   - Copy the example configuration file:
     ```bash
     cp config.yaml.example config.yaml
     ```
   - Edit `config.yaml` to set your database URL, Redis configuration, and server port

4. **Build the application**
   ```bash
   go build -o ai-router
   ```

5. **Run the application**
   ```bash
   ./ai-router
   ```

### Configuration Details

The application uses a YAML configuration file (`config.yaml`) with the following sections:

- **Server**: HTTP server settings
  - `port`: The port on which the server will listen (default: "8315")
  - `shutdown_timeout`: Graceful shutdown timeout (default: 10s)

- **Database**: PostgreSQL connection settings
  - `url`: PostgreSQL connection URL
  - `max_idle_conns`: Maximum number of idle connections (default: 10)
  - `max_open_conns`: Maximum number of open connections (default: 100)
  - `conn_max_lifetime`: Connection lifetime (default: 1h)

- **Redis**: Redis connection settings (optional)
  - `url`: Redis server URL
  - `password`: Redis password
  - `db`: Redis database number
  - `billing_queue_key`: Key for the billing queue

## Testing Information

### Running Tests

The project uses Go's standard testing package. To run tests:

```bash
# Run all tests
go test ./...

# Run tests for a specific package
go test ./const

# Run tests with verbose output
go test -v ./...

# Run tests with coverage
go test -cover ./...

# Generate a coverage report
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

### Writing Tests

1. **Create test files**: Test files should be named with the `_test.go` suffix and placed in the same package as the code being tested.

2. **Test function naming**: Test functions should be named `TestXxx` where `Xxx` is the function or feature being tested.

3. **Table-driven tests**: Use table-driven tests for testing multiple cases with the same logic.

Example of a simple test:

```go
package _const

import (
	"testing"
)

func TestProviderTypes(t *testing.T) {
	// Test that provider types have unique values
	providerTypes := []ProviderType{
		ProviderOpenAICompatibility,
		ProviderGemini,
		ProviderOpenAI,
		// ...
	}

	// Create a map to check for duplicates
	seen := make(map[ProviderType]bool)
	for _, pt := range providerTypes {
		if seen[pt] {
			t.Errorf("Duplicate provider type value: %d", pt)
		}
		seen[pt] = true
	}
}
```

### Testing with Database Dependencies

For tests that require a database:

1. Use an in-memory SQLite database or a test PostgreSQL instance
2. Create test fixtures for required data
3. Clean up after tests to ensure isolation

Example of a database test setup:

```go
func setupTestDB() (*gorm.DB, error) {
	// Use an in-memory SQLite database for testing
	db, err := gorm.Open(sqlite.Open("file::memory:?cache=shared"), &gorm.Config{})
	if err != nil {
		return nil, err
	}
	
	// Initialize the schema
	if err := models.InitDB(db); err != nil {
		return nil, err
	}
	
	return db, nil
}
```

## Additional Development Information

### Project Structure

- **api/**: HTTP API handlers
- **billing/**: Billing-related functionality
- **config/**: Configuration loading and management
- **const/**: Constants and enumerations
- **core/**: Core functionality like rate limiting
- **models/**: Database models and ORM functionality
- **provider/**: Provider-specific implementations
- **router/**: HTTP routing setup

### Code Style and Conventions

1. **Error Handling**: Always check and handle errors. Use descriptive error messages.

2. **Logging**: Use the standard `log` package for logging. Include context in log messages.

3. **Configuration**: Use the configuration system in `config/` rather than hardcoding values.

4. **Database Access**: Use GORM for database operations. Follow the patterns in the `models/` package.

5. **API Responses**: Follow the response formats in the `api/` package for consistency.

### Provider Implementation

When adding a new provider:

1. Add a new provider type constant in `const/const.go`
2. Create a new provider implementation in the `provider/` directory
3. Register the provider in `provider/provider.go`
4. Add default models for the provider in `models/models.go` (SeedDefaultProviders function)

### Debugging

1. **Enable verbose logging**: Use the `-v` flag when running the application for more detailed logs.

2. **Database debugging**: Set GORM to debug mode in development:
   ```go
   db, err := gorm.Open(postgres.Open(dbURL), &gorm.Config{
       Logger: logger.Default.LogMode(logger.Info),
   })
   ```

3. **API debugging**: Use tools like Postman or curl to test API endpoints directly.

4. **Rate limiting**: Monitor rate limiter behavior using the logs when troubleshooting capacity issues.