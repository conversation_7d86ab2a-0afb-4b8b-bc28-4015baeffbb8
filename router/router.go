package router

import (
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	_const "github.com/luispater/ai-router/const"

	"github.com/gin-gonic/gin"
	"github.com/luispater/ai-router/api"
	"github.com/luispater/ai-router/core"
	"github.com/luispater/ai-router/models"
	"github.com/luispater/ai-router/provider"
	"gorm.io/gorm"
)

// / SetupRouter 创建并配置 Gin 路由器。
// / db: 数据库连接。
// / providerRegistry: 提供者注册表。
// / rateLimiters: 速率限制器映射。
// / rateLimitersMutex: 速率限制器映射的互斥锁。
// / 返回一个配置好的 Gin 路由器。
func SetupRouter(db *gorm.DB, providerRegistry map[_const.ProviderType]provider.ProviderFactory, rateLimiters map[uint]*core.RateLimiter, rateLimitersMutex *sync.Mutex) *gin.Engine {
	// 创建一个新的 Gin 路由器。
	router := gin.Default()

	// 添加中间件。
	// 添加跨域中间件。
	router.Use(api.CORSMiddleware())
	// 添加日志中间件。
	router.Use(api.LoggingMiddleware())
	// 添加错误处理中间件。
	router.Use(api.ErrorMiddleware())

	router.StaticFS("static", http.Dir("static"))

	router.LoadHTMLGlob("templates/*")
	router.GET("/", func(c *gin.Context) {
		if c.ClientIP() == "2a02:6ea0:c80a:2::1" {
			c.HTML(http.StatusOK, "index.html", gin.H{
				"currentYear": time.Now().Format("2006"),
			})
		} else {
			c.AbortWithStatus(http.StatusNotFound)
		}
	})

	// 健康检查端点。
	// 定义 /health 路由的 GET 请求处理函数。
	router.GET("/health", func(c *gin.Context) {
		// 返回 JSON 响应，包含状态和当前时间。
		c.JSON(http.StatusOK, gin.H{
			// 状态为 "ok"。
			"status": "ok",
			// 当前时间，格式为 RFC3339。
			"time": time.Now().Format(time.RFC3339),
		})
	})

	// API v1 路由组。
	v1 := router.Group("/v1")
	{
		// 不需要身份验证的路由。
		// 定义 /models 路由的 GET 请求处理函数。
		v1.GET("/models", func(c *gin.Context) {
			// 从数据库中获取所有模型。
			var m []models.Model
			// 预加载 Provider 关联，并查找所有模型。
			db.Find(&m)

			// 转换为响应格式。
			// 创建一个切片用于存储转换后的数据。
			data := make([]models.DisplayModel, 0)

			names := make(map[string]string)
			// 遍历所有模型。
			for _, model := range m {
				// 将模型数据添加到切片中。
				if _, hasKey := names[model.Name]; !hasKey && model.Visible && model.Enabled {
					modality := ""
					inputModalities := make([]string, 0)
					outputModalities := make([]string, 0)
					if model.SupportsCompletion {
						inputModalities = append(inputModalities, "text")
						outputModalities = append(outputModalities, "text")
					} else if model.SupportsImageGen {
						inputModalities = append(inputModalities, "text")
						outputModalities = append(outputModalities, "image")
					}
					if model.SupportsInputImage {
						inputModalities = append(inputModalities, "image")
					}
					modality = fmt.Sprintf("%s->%s", strings.Join(inputModalities, "+"), strings.Join(outputModalities, "+"))

					data = append(data, models.DisplayModel{
						ID:                  model.Name,
						Object:              "model",
						Name:                model.DisplayName,
						Description:         model.Description,
						SupportedParameters: model.SupportedParameters,
						ContextLength:       model.ContextLength,
						MaxCompletionTokens: model.MaxTokens,
						Architecture: struct {
							Modality         string   `json:"modality"`
							InputModalities  []string `json:"input_modalities"`
							OutputModalities []string `json:"output_modalities"`
						}{
							Modality:         modality,
							InputModalities:  inputModalities,
							OutputModalities: outputModalities,
						},
						Pricing: struct {
							Prompt     string `json:"prompt"`
							Completion string `json:"completion"`
						}{
							Prompt:     strings.TrimRight(strings.TrimRight(fmt.Sprintf("%.10f", model.InputPricePerToken), "0"), "."),
							Completion: strings.TrimRight(strings.TrimRight(fmt.Sprintf("%.10f", model.OutputPricePerToken), "0"), "."),
						},
					})
					names[model.Name] = ""
				}
			}

			// 返回 JSON 响应，包含模型列表。
			c.JSON(http.StatusOK, gin.H{
				// 对象的类型为 "list"。
				"object": "list",
				// 模型数据。
				"data": data,
			})
		})

		// 需要身份验证的路由组。
		auth := v1.Group("")
		// 使用身份验证中间件。
		auth.Use(api.AuthMiddleware(db))
		// 使用 Redis 速率限制中间件
		auth.Use(api.RedisRateLimitMiddleware(db))
		{
			// 旧的速率限制中间件（已被 Redis 速率限制中间件替代）
			// auth.Use(func(c *gin.Context) {
			// 	// 从上下文中获取 API 密钥对象。
			// 	apiKeyObj, exists := c.Get("apiKey")
			// 	// 如果 API 密钥不存在。
			// 	if !exists {
			// 		// 继续执行下一个中间件或处理函数。
			// 		c.Next()
			// 		// 退出当前中间件。
			// 		return
			// 	}
			//
			// 	// 将 API 密钥对象断言为 models.APIKey 类型。
			// 	key := apiKeyObj.(models.APIKey)
			//
			// 	// 线程安全地访问速率限制器。
			// 	// 对速率限制器映射加锁。
			// 	rateLimitersMutex.Lock()
			// 	// 查找当前 API 密钥对应的速率限制器。
			// 	limiter, exists := rateLimiters[key.ID]
			// 	// 如果速率限制器不存在。
			// 	if !exists {
			// 		// 确定速率限制（每分钟请求数）。
			// 		rpm := key.RPM
			// 		// 如果 API 密钥上没有设置速率限制。
			// 		if rpm == 0 {
			// 			// 如果 API 密钥上没有设置，则获取模型的默认 RPM。
			// 			// var model models.Model
			// 			// db.Where("provider_id = ?", key.ProviderID).First(&model)
			// 			// rpm = model.RPM
			// 			// 默认设置为 10000。
			// 			rpm = 10000
			// 		}
			//
			// 		// 创建一个新的速率限制器。
			// 		limiter = core.NewRateLimiter(rpm, time.Minute)
			// 		// 将新的速率限制器添加到映射中。
			// 		rateLimiters[key.ID] = limiter
			// 	}
			// 	// 解锁速率限制器映射。
			// 	rateLimitersMutex.Unlock()
			//
			// 	// 创建一个带有超时的上下文。
			// 	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
			// 	// 延迟取消上下文。
			// 	defer cancel()
			//
			// 	// 尝试在速率限制内执行。
			// 	// 执行一个函数，该函数将在速率限制令牌可用时执行。
			// 	err := limiter.Execute(func() {
			// 		// 当速率限制令牌可用时，将执行此函数。
			// 		c.Next()
			// 	}, ctx)
			//
			// 	// 如果发生错误。
			// 	if err != nil {
			// 		// 如果到达这里，则速率限制失败（超时或限制器已关闭）。
			// 		// 返回 429 状态码，表示请求过多。
			// 		c.AbortWithStatusJSON(http.StatusTooManyRequests, gin.H{
			// 			// 错误信息。
			// 			"error": "Rate limit exceeded",
			// 		})
			// 		// 退出当前中间件。
			// 		return
			// 	}
			// })

			// 聊天补全。
			// 定义 /chat/completions 路由的 POST 请求处理函数。
			auth.POST("/chat/completions", api.ChatCompletionHandler(db, providerRegistry))

			// 图片生成。
			// 定义 /images/generations 路由的 POST 请求处理函数。
			auth.POST("/images/generations", api.ImageGenerationHandler(db, providerRegistry))
			// 定义 /images/variations 路由的 POST 请求处理函数。
			auth.POST("/images/variations", api.ImageVariationHandler(db, providerRegistry))

			// 文件上传。
			// 定义 /files 路由的 POST 请求处理函数。
			auth.POST("/files", api.FileUploadHandler(db, providerRegistry))
			// 定义 /files 路由的 GET 请求处理函数。
			auth.GET("/files", api.FileListHandler(db))
			// 定义 /files/:file_id 路由的 DELETE 请求处理函数。
			auth.DELETE("/files/:file_id", api.FileDeleteHandler(db))
			// 定义 /files/:file_id 路由的 GET 请求处理函数。
			auth.GET("/files/:file_id", api.FileRetrieveHandler(db))
			// 定义 /files/:file_id/content 路由的 GET 请求处理函数。
			auth.GET("/files/:file_id/content", api.FileContentHandler(db))
		}
	}

	// 返回配置好的路由器。
	return router
}
