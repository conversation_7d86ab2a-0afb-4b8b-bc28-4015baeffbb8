# 对项目进行升级
## @models/user.go User结构中增加Credits字段，类型为float64，该字段作为用户剩余余额字段
## @api/chat.go 中，在recordUsage方法中，按照@models/model.go中Model结构中定义的InputPricePerToken、OutputPricePerToken金额，扣除对应的余额
## @api/images.go 中，在recordImageUsage方法中，以每次1个输出token，按照@models/model.go中Model结构中定义的OutputPricePerToken金额，扣除对应的余额
## @api/chat.go 中，ChatCompletionHandler的方法，当要调用的Model的InputPricePerToken、OutputPricePerToken不为0，则需要检查用户余额是否充足，如果不足，则返回402错误
## @api/images.go 中，ImageGenerationHandler的方法，当要调用的Model的InputPricePerToken、OutputPricePerToken不为0，则需要检查用户余额是否充足，如果不足，则返回402错误
