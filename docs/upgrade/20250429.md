# 对项目进行功能升级
## models/apikey.go 增加了RPS、TPS、RPH、TPH、RPD、TPD，他们用来做用户请求量限制
### 当用户进行API请求时，现在已经在数据库中记录了用户使用的TOKEN数，现在需要在REDIS中也记录一份当前的使用TOKEN数
### REDIS中按照以下规则进行存储
#### RPM: {apikey}_{202504191723}_R 其中202504191723时当前时间精确到分钟，存储有效期是90秒过期，存储的内容是该分钟累积的请求次数
#### TPM: {apikey}_{202504191723}_T 其中202504191723时当前时间精确到分钟，存储有效期是90秒过期，存储的内容是该分钟累积的请求TOKEN数，包括INPUT和OUTPUT
#### RPH: {apikey}_{2025041917}_R 其中2025041917时当前时间精确到时，存储有效期是61分钟过期，存储的内容是该小时累积的请求次数
#### TPH: {apikey}_{2025041917}_T 其中2025041917时当前时间精确到时，存储有效期是61分钟过期，存储的内容是该小时累积的请求TOKEN数，包括INPUT和OUTPUT
#### RPH: {apikey}_{20250419}_R 其中20250419时当前时间精确到日，存储有效期是1441分钟过期，存储的内容是该日累积的请求次数
#### TPH: {apikey}_{20250419}_T 其中20250419时当前时间精确到日，存储有效期是1441分钟过期，存储的内容是该日累积的请求TOKEN数，包括INPUT和OUTPUT
### RPS和TPS分别在golang代码中实现
#### RPS和TPS分别增加读写锁
### 数据库中如果对应的数值是0，则不进行控制，如果不为0，则根据以下逻辑控制：
#### 如果REDIS、golang代码中的，当前周期的计数为0，则运行正常请求
#### 如果REDIS、golang代码中的，当前周期的计数大于等于数据库中的数值，则返回http 429错误，提示用户当前请求被限制
### 在http response增加header信息：
#### x-rps-limits、x-rpm-limits、x-rph-limits、x-rpd-limits、x-tps-limits、x-tpm-limits、x-tph-limits、x-tpd-limits，分别为对应的限制数量
### 在http response增加header信息：
#### x-rps-remain、x-rpm-remain、x-rph-remain、x-rpd-remain、x-tps-remain、x-tpm-remain、x-tph-remain、x-tpd-remain，分别为对应的剩余限制数量
## 将上面的逻辑与 @api/middleware.go 中的 AuthMiddleware 做整合，不需要额外新建middleware