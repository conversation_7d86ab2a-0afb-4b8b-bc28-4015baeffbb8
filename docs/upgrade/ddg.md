curl 'https://duckduckgo.com/duckchat/v1/chat' \
-H 'accept: text/event-stream' \
-H 'content-type: application/json' \
-H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.37' \
-H 'x-vqd-4: 4-288575559322128795709458074779459388568' \
--data-raw '{"model":"claude-3-haiku-20240307","messages":[{"role":"user","content":"完全根据我的要求回复"},{"role":"assistant","content":"OK"},{"role":"user","content":"说一个20字的故事"}]}'

curl 'https://duckduckgo.com/duckchat/v1/chat' \
-H 'accept: text/event-stream' \
-H 'content-type: application/json' \
-H 'user-agent: Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.37' \
-H 'x-vqd-4: 4-288575559322128795709458074779459388547' \
--data-raw '{"model":"gpt-4o-mini","messages":[{"role":"user","content":"完全根据我的要求回复"},{"role":"assistant","content":"OK"},{"role":"user","content":"说一个20字的故事"}]}'


curl 'https://duckduckgo.com/duckchat/v1/chat' \
-H 'accept: text/event-stream' \
-H 'content-type: application/json' \
-H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.37' \
-H 'x-vqd-4: 4-314170687124593159670704114440227489064' \
--data-raw '{"model":"o3-mini","messages":[{"role":"user","content":"完全根据我的要求回复"},{"role":"assistant","content":"OK"},{"role":"user","content":"说一个2000字的故事"}]}'

