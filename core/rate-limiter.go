package core

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// RateLimiter 实现可配置时间单位的速率控制
type RateLimiter struct {
	limit        int            // 每个时间单位允许的请求数
	timeUnit     time.Duration  // 时间单位（秒、分钟、小时或自定义）
	mutex        sync.Mutex     // 互斥锁，保证并发安全
	tokenCh      chan struct{}  // 令牌通道
	closeCh      chan struct{}  // 用于关闭限流器的信号
	wg           sync.WaitGroup // 用于等待所有任务完成
	closed       bool           // 标记限流器是否已关闭
	pendingTasks int            // 当前等待中的任务数量
	pendingMutex sync.Mutex     // 用于保护pendingTasks的并发访问
}

// NewRateLimiter 创建一个新的限速器
// limit: 在给定时间单位内允许的请求数
// timeUnit: 时间单位，如time.Second, time.Minute, time.Hour或自定义
func NewRateLimiter(limit int, timeUnit time.Duration) *RateLimiter {
	if limit <= 0 {
		limit = 1
	}

	limiter := &RateLimiter{
		limit:        limit,
		timeUnit:     timeUnit,
		tokenCh:      make(chan struct{}, limit), // 令牌通道缓冲大小等于限制值
		closeCh:      make(chan struct{}),
		pendingTasks: 0,
	}

	// 初始填充令牌通道
	for i := 0; i < limit; i++ {
		limiter.tokenCh <- struct{}{}
	}

	// 启动令牌生成器
	go limiter.tokenGenerator()

	return limiter
}

// 令牌生成器，负责按照限流速率生成新的令牌
func (rl *RateLimiter) tokenGenerator() {
	// 计算令牌生成间隔
	interval := time.Duration(float64(rl.timeUnit) / float64(rl.limit))
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-rl.closeCh:
			return
		case <-ticker.C:
			// 尝试向令牌通道添加一个令牌
			select {
			case rl.tokenCh <- struct{}{}:
				// 令牌添加成功
			default:
				// 通道已满，说明消费速度慢于生成速度，丢弃这个令牌
			}
		}
	}
}

// Close 关闭限流器并等待所有正在执行的任务完成
func (rl *RateLimiter) Close() {
	rl.mutex.Lock()
	if !rl.closed {
		rl.closed = true
		close(rl.closeCh)
	}
	rl.mutex.Unlock()

	rl.wg.Wait() // 等待所有任务完成
}

// getToken 获取一个令牌，如果没有可用的令牌则阻塞
func (rl *RateLimiter) getToken(ctx context.Context) error {
	select {
	case <-rl.tokenCh:
		// 获取到令牌
		return nil
	case <-ctx.Done():
		// 上下文已取消
		return ctx.Err()
	}
}

// GetQueueLength 返回当前等待执行的任务数量
func (rl *RateLimiter) GetQueueLength() int {
	rl.pendingMutex.Lock()
	defer rl.pendingMutex.Unlock()
	return rl.pendingTasks
}

// ExecuteWithoutResult 提交一个不需要返回结果的任务
// 适用于"执行后忘记"的场景
func (rl *RateLimiter) Execute(fn func(), ctx context.Context) error {
	if ctx == nil {
		ctx = context.Background()
	}

	// 检查限流器是否已关闭
	rl.mutex.Lock()
	if rl.closed {
		rl.mutex.Unlock()
		return fmt.Errorf("rate limiter is closed")
	}
	rl.mutex.Unlock()

	// 增加等待任务计数
	rl.pendingMutex.Lock()
	rl.pendingTasks++
	rl.pendingMutex.Unlock()

	// 标记一个任务开始执行
	rl.wg.Add(1)

	// 在新的goroutine中执行任务
	go func() {
		defer rl.wg.Done()
		defer func() {
			// 减少等待任务计数
			rl.pendingMutex.Lock()
			rl.pendingTasks--
			rl.pendingMutex.Unlock()
		}()

		// 尝试获取令牌
		if err := rl.getToken(ctx); err != nil {
			// 获取令牌失败，不执行任务
			return
		}

		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			// 上下文已取消，不执行任务
			return
		default:
			// 上下文未取消，继续执行任务
		}

		// 执行任务
		fn()
	}()

	return nil
}
