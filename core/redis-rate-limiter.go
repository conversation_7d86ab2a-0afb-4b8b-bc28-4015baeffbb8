package core

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
)

// apiKeyCounter stores per-API key counters and timestamps
type apiKeyCounter struct {
	counter   int
	timestamp time.Time
	lock      sync.RWMutex
}

// RedisRateLimiter implements rate limiting using Redis
type RedisRateLimiter struct {
	client *redis.Client
	// For RPS and TPS in-memory tracking
	rpsCounters map[string]*apiKeyCounter // map[apiKey]*apiKeyCounter
	tpsCounters map[string]*apiKeyCounter // map[apiKey]*apiKeyCounter
	countersLock sync.RWMutex             // Lock for the maps themselves
}

// NewRedisRateLimiter creates a new Redis-based rate limiter
func NewRedisRateLimiter(client *redis.Client) *RedisRateLimiter {
	return &RedisRateLimiter{
		client:      client,
		rpsCounters: make(map[string]*apiKeyCounter),
		tpsCounters: make(map[string]*apiKeyCounter),
	}
}

// formatKey formats a Redis key according to the specified pattern
// pattern can be "minute", "hour", or "day"
// type can be "R" for requests or "T" for tokens
func formatKey(apiKey string, pattern string, keyType string) string {
	now := time.Now()
	var timeFormat string

	switch pattern {
	case "minute":
		timeFormat = now.Format("200601021504") // YYYYMMDDHHMM
	case "hour":
		timeFormat = now.Format("2006010215") // YYYYMMDDHH
	case "day":
		timeFormat = now.Format("20060102") // YYYYMMDD
	default:
		timeFormat = now.Format("200601021504") // Default to minute
	}

	return fmt.Sprintf("%s_%s_%s", apiKey, timeFormat, keyType)
}

// getExpiry returns the expiry duration based on the pattern
func getExpiry(pattern string) time.Duration {
	switch pattern {
	case "minute":
		return 90 * time.Second
	case "hour":
		return 61 * time.Minute
	case "day":
		return 1441 * time.Minute
	default:
		return 90 * time.Second // Default to minute
	}
}

// IncrementRequestCount increments the request count in Redis
func (rl *RedisRateLimiter) IncrementRequestCount(ctx context.Context, apiKey string) error {
	// Increment minute counter
	rpmKey := formatKey(apiKey, "minute", "R")
	if err := rl.client.Incr(ctx, rpmKey).Err(); err != nil {
		return fmt.Errorf("failed to increment RPM: %w", err)
	}
	rl.client.Expire(ctx, rpmKey, getExpiry("minute"))

	// Increment hour counter
	rphKey := formatKey(apiKey, "hour", "R")
	if err := rl.client.Incr(ctx, rphKey).Err(); err != nil {
		return fmt.Errorf("failed to increment RPH: %w", err)
	}
	rl.client.Expire(ctx, rphKey, getExpiry("hour"))

	// Increment day counter
	rpdKey := formatKey(apiKey, "day", "R")
	if err := rl.client.Incr(ctx, rpdKey).Err(); err != nil {
		return fmt.Errorf("failed to increment RPD: %w", err)
	}
	rl.client.Expire(ctx, rpdKey, getExpiry("day"))

	// Increment RPS counter (in memory)
	// Get or create counter for this API key
	rl.countersLock.RLock()
	counter, exists := rl.rpsCounters[apiKey]
	rl.countersLock.RUnlock()

	if !exists {
		// Need to create a new counter
		rl.countersLock.Lock()
		// Check again in case another goroutine created it while we were waiting for the lock
		counter, exists = rl.rpsCounters[apiKey]
		if !exists {
			counter = &apiKeyCounter{
				counter:   0,
				timestamp: time.Now(),
			}
			rl.rpsCounters[apiKey] = counter
		}
		rl.countersLock.Unlock()
	}

	// Now use the counter's lock
	counter.lock.Lock()
	defer counter.lock.Unlock()

	now := time.Now()
	if now.Sub(counter.timestamp) >= time.Second {
		// Reset counter for new second
		counter.counter = 1
		counter.timestamp = now
	} else {
		// Increment counter within the same second
		counter.counter++
	}

	return nil
}

// IncrementTokenCount increments the token count in Redis
func (rl *RedisRateLimiter) IncrementTokenCount(ctx context.Context, apiKey string, tokens int) error {
	// Increment minute counter
	tpmKey := formatKey(apiKey, "minute", "T")
	if err := rl.client.IncrBy(ctx, tpmKey, int64(tokens)).Err(); err != nil {
		return fmt.Errorf("failed to increment TPM: %w", err)
	}
	rl.client.Expire(ctx, tpmKey, getExpiry("minute"))

	// Increment hour counter
	tphKey := formatKey(apiKey, "hour", "T")
	if err := rl.client.IncrBy(ctx, tphKey, int64(tokens)).Err(); err != nil {
		return fmt.Errorf("failed to increment TPH: %w", err)
	}
	rl.client.Expire(ctx, tphKey, getExpiry("hour"))

	// Increment day counter
	tpdKey := formatKey(apiKey, "day", "T")
	if err := rl.client.IncrBy(ctx, tpdKey, int64(tokens)).Err(); err != nil {
		return fmt.Errorf("failed to increment TPD: %w", err)
	}
	rl.client.Expire(ctx, tpdKey, getExpiry("day"))

	// Increment TPS counter (in memory)
	// Get or create counter for this API key
	rl.countersLock.RLock()
	counter, exists := rl.tpsCounters[apiKey]
	rl.countersLock.RUnlock()

	if !exists {
		// Need to create a new counter
		rl.countersLock.Lock()
		// Check again in case another goroutine created it while we were waiting for the lock
		counter, exists = rl.tpsCounters[apiKey]
		if !exists {
			counter = &apiKeyCounter{
				counter:   0,
				timestamp: time.Now(),
			}
			rl.tpsCounters[apiKey] = counter
		}
		rl.countersLock.Unlock()
	}

	// Now use the counter's lock
	counter.lock.Lock()
	defer counter.lock.Unlock()

	now := time.Now()
	if now.Sub(counter.timestamp) >= time.Second {
		// Reset counter for new second
		counter.counter = tokens
		counter.timestamp = now
	} else {
		// Increment counter within the same second
		counter.counter += tokens
	}

	return nil
}

// GetRequestCount gets the current request count from Redis
func (rl *RedisRateLimiter) GetRequestCount(ctx context.Context, apiKey string, pattern string) (int, error) {
	key := formatKey(apiKey, pattern, "R")
	val, err := rl.client.Get(ctx, key).Result()
	if err == redis.Nil {
		// Key does not exist, return 0
		return 0, nil
	} else if err != nil {
		return 0, fmt.Errorf("failed to get request count: %w", err)
	}

	count, err := strconv.Atoi(val)
	if err != nil {
		return 0, fmt.Errorf("failed to parse request count: %w", err)
	}

	return count, nil
}

// GetTokenCount gets the current token count from Redis
func (rl *RedisRateLimiter) GetTokenCount(ctx context.Context, apiKey string, pattern string) (int, error) {
	key := formatKey(apiKey, pattern, "T")
	val, err := rl.client.Get(ctx, key).Result()
	if err == redis.Nil {
		// Key does not exist, return 0
		return 0, nil
	} else if err != nil {
		return 0, fmt.Errorf("failed to get token count: %w", err)
	}

	count, err := strconv.Atoi(val)
	if err != nil {
		return 0, fmt.Errorf("failed to parse token count: %w", err)
	}

	return count, nil
}

// GetRPS gets the current requests per second (from memory)
func (rl *RedisRateLimiter) GetRPS(apiKey string) int {
	// Get counter for this API key
	rl.countersLock.RLock()
	counter, exists := rl.rpsCounters[apiKey]
	rl.countersLock.RUnlock()

	if !exists {
		return 0
	}

	// Use the counter's lock
	counter.lock.RLock()
	defer counter.lock.RUnlock()

	// If the timestamp is more than a second old, return 0
	if time.Since(counter.timestamp) >= time.Second {
		return 0
	}

	return counter.counter
}

// GetTPS gets the current tokens per second (from memory)
func (rl *RedisRateLimiter) GetTPS(apiKey string) int {
	// Get counter for this API key
	rl.countersLock.RLock()
	counter, exists := rl.tpsCounters[apiKey]
	rl.countersLock.RUnlock()

	if !exists {
		return 0
	}

	// Use the counter's lock
	counter.lock.RLock()
	defer counter.lock.RUnlock()

	// If the timestamp is more than a second old, return 0
	if time.Since(counter.timestamp) >= time.Second {
		return 0
	}

	return counter.counter
}

// CheckRateLimits checks if the request exceeds any rate limits
// Returns true if the request is allowed, false if it exceeds limits
func (rl *RedisRateLimiter) CheckRateLimits(ctx context.Context, apiKey string, limits map[string]int) (bool, map[string]int, map[string]int, error) {
	// If all limits are 0, allow the request
	allZero := true
	for _, limit := range limits {
		if limit > 0 {
			allZero = false
			break
		}
	}
	if allZero {
		return true, limits, make(map[string]int), nil
	}

	// Check RPS limit
	if rpsLimit, ok := limits["rps"]; ok && rpsLimit > 0 {
		rps := rl.GetRPS(apiKey)
		if rps > rpsLimit {
			return false, limits, map[string]int{"rps": 0}, nil
		}
	}

	// Check TPS limit
	if tpsLimit, ok := limits["tps"]; ok && tpsLimit > 0 {
		tps := rl.GetTPS(apiKey)
		if tps > tpsLimit {
			return false, limits, map[string]int{"tps": 0}, nil
		}
	}

	// Check RPM limit
	if rpmLimit, ok := limits["rpm"]; ok && rpmLimit > 0 {
		rpm, err := rl.GetRequestCount(ctx, apiKey, "minute")
		if err != nil {
			return false, limits, nil, err
		}
		if rpm > rpmLimit {
			return false, limits, map[string]int{"rpm": 0}, nil
		}
	}

	// Check TPM limit
	if tpmLimit, ok := limits["tpm"]; ok && tpmLimit > 0 {
		tpm, err := rl.GetTokenCount(ctx, apiKey, "minute")
		if err != nil {
			return false, limits, nil, err
		}
		if tpm > tpmLimit {
			return false, limits, map[string]int{"tpm": 0}, nil
		}
	}

	// Check RPH limit
	if rphLimit, ok := limits["rph"]; ok && rphLimit > 0 {
		rph, err := rl.GetRequestCount(ctx, apiKey, "hour")
		if err != nil {
			return false, limits, nil, err
		}
		if rph > rphLimit {
			return false, limits, map[string]int{"rph": 0}, nil
		}
	}

	// Check TPH limit
	if tphLimit, ok := limits["tph"]; ok && tphLimit > 0 {
		tph, err := rl.GetTokenCount(ctx, apiKey, "hour")
		if err != nil {
			return false, limits, nil, err
		}
		if tph > tphLimit {
			return false, limits, map[string]int{"tph": 0}, nil
		}
	}

	// Check RPD limit
	if rpdLimit, ok := limits["rpd"]; ok && rpdLimit > 0 {
		rpd, err := rl.GetRequestCount(ctx, apiKey, "day")
		if err != nil {
			return false, limits, nil, err
		}
		if rpd > rpdLimit {
			return false, limits, map[string]int{"rpd": 0}, nil
		}
	}

	// Check TPD limit
	if tpdLimit, ok := limits["tpd"]; ok && tpdLimit > 0 {
		tpd, err := rl.GetTokenCount(ctx, apiKey, "day")
		if err != nil {
			return false, limits, nil, err
		}
		if tpd > tpdLimit {
			return false, limits, map[string]int{"tpd": 0}, nil
		}
	}

	// Calculate remaining limits
	remaining := make(map[string]int)

	// RPS remaining
	if rpsLimit, ok := limits["rps"]; ok && rpsLimit > 0 {
		remaining["rps"] = rpsLimit - rl.GetRPS(apiKey)
	}

	// TPS remaining
	if tpsLimit, ok := limits["tps"]; ok && tpsLimit > 0 {
		remaining["tps"] = tpsLimit - rl.GetTPS(apiKey)
	}

	// RPM remaining
	if rpmLimit, ok := limits["rpm"]; ok && rpmLimit > 0 {
		rpm, _ := rl.GetRequestCount(ctx, apiKey, "minute")
		remaining["rpm"] = rpmLimit - rpm
	}

	// TPM remaining
	if tpmLimit, ok := limits["tpm"]; ok && tpmLimit > 0 {
		tpm, _ := rl.GetTokenCount(ctx, apiKey, "minute")
		remaining["tpm"] = tpmLimit - tpm
	}

	// RPH remaining
	if rphLimit, ok := limits["rph"]; ok && rphLimit > 0 {
		rph, _ := rl.GetRequestCount(ctx, apiKey, "hour")
		remaining["rph"] = rphLimit - rph
	}

	// TPH remaining
	if tphLimit, ok := limits["tph"]; ok && tphLimit > 0 {
		tph, _ := rl.GetTokenCount(ctx, apiKey, "hour")
		remaining["tph"] = tphLimit - tph
	}

	// RPD remaining
	if rpdLimit, ok := limits["rpd"]; ok && rpdLimit > 0 {
		rpd, _ := rl.GetRequestCount(ctx, apiKey, "day")
		remaining["rpd"] = rpdLimit - rpd
	}

	// TPD remaining
	if tpdLimit, ok := limits["tpd"]; ok && tpdLimit > 0 {
		tpd, _ := rl.GetTokenCount(ctx, apiKey, "day")
		remaining["tpd"] = tpdLimit - tpd
	}

	return true, limits, remaining, nil
}
