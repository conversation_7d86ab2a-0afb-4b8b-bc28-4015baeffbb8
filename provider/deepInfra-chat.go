package provider

//
// import (
// 	"context"
// 	"errors"
// 	_const "github.com/luispater/ai-router/const"
// 	"github.com/luispater/ai-router/models"
// 	"gorm.io/gorm"
// 	"io"
// 	"mime/multipart"
// )
//
// // DeepInfraChatProvider implements the Provider interface for OpenAI models
// type DeepInfraChatProvider struct {
// 	OpenAICompatibility
// 	models map[string][]ModelType
// 	db     *gorm.DB
// }
//
// // NewDeepInfraChatProvider creates a new OpenAI provider
// func NewDeepInfraChatProvider(db *gorm.DB) (Provider, error) {
// 	// Define supported models and their capabilities
// 	m := map[string][]ModelType{}
//
// 	var providerModels []models.Model
// 	result := db.Where("provider_id=?", _const.ProviderDeepInfraChat).Find(&providerModels)
// 	if result.Error != nil {
// 		return nil, result.Error
// 	}
//
// 	for _, model := range providerModels {
// 		var capabilities []ModelType
//
// 		if model.SupportsChat {
// 			capabilities = append(capabilities, ModelTypeChat)
// 		}
// 		if model.SupportsCompletion {
// 			capabilities = append(capabilities, ModelTypeCompletion)
// 		}
// 		if model.SupportsEmbedding {
// 			capabilities = append(capabilities, ModelTypeEmbedding)
// 		}
// 		if model.SupportsImageGen {
// 			capabilities = append(capabilities, ModelTypeImageGen)
// 		}
// 		if model.SupportsImageEdit {
// 			capabilities = append(capabilities, ModelTypeImageEdit)
// 		}
// 		if model.SupportsImageVar {
// 			capabilities = append(capabilities, ModelTypeImageVar)
// 		}
// 		if model.SupportsAudioTrans {
// 			capabilities = append(capabilities, ModelTypeAudioTrans)
// 		}
// 		if model.SupportsAudioTrans2 {
// 			capabilities = append(capabilities, ModelTypeAudioTrans2)
// 		}
//
// 		m[model.Name] = capabilities
// 	}
//
// 	return &DeepInfraChatProvider{
// 		models: m,
// 		db:     db,
// 	}, nil
// }
//
// // Register the provider
// func init() {
// 	RegisterProvider(_const.ProviderDeepInfraChat, NewDeepInfraChatProvider)
// }
//
// // GetProviderType returns the type of the provider
// func (p *DeepInfraChatProvider) GetProviderType() _const.ProviderType {
// 	return _const.ProviderDeepInfraChat
// }
//
// // GetSupportedModels returns the models supported by this provider
// func (p *DeepInfraChatProvider) GetSupportedModels() map[string][]ModelType {
// 	return p.models
// }
//
// // CreateChatCompletion creates a chat completion
// func (p *DeepInfraChatProvider) CreateChatCompletion(ctx context.Context, request []byte, model models.Model, usage *Usage) ([]byte, error) {
// 	p.SetBaseUrl("https://api.deepinfra.com/v1/openai")
// 	return p.OpenAICompatibility.CreateChatCompletion(ctx, request, model, usage)
// }
//
// // CreateChatCompletionStream creates a streaming chat completion
// func (p *DeepInfraChatProvider) CreateChatCompletionStream(ctx context.Context, request []byte, model models.Model, usage *Usage) (io.ReadCloser, error) {
// 	p.SetBaseUrl("https://api.deepinfra.com/v1/openai")
// 	return p.OpenAICompatibility.CreateChatCompletionStream(ctx, request, model, usage)
// }
//
// // CreateImage generates images based on a prompt
// func (p *DeepInfraChatProvider) CreateImage(ctx context.Context, request ImageGenerationRequest) (*ImageResponse, error) {
// 	return nil, errors.New("image generation not supported by DeepInfraChat provider")
// }
//
// // ProcessFile handles file uploads
// func (p *DeepInfraChatProvider) ProcessFile(ctx context.Context, file *multipart.FileHeader, purpose string) (string, error) {
// 	return "", errors.New("file processing not implemented for DeepInfraChat provider")
// }
//
// // Close closes any resources used by the provider
// func (p *DeepInfraChatProvider) Close() error {
// 	return nil
// }
