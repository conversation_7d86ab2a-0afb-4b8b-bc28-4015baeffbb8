package provider

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net"
	"net/http"
	"sync"
	"time"

	_const "github.com/luispater/ai-router/const"
	"github.com/luispater/ai-router/models"
	"github.com/tidwall/gjson"
	"github.com/tidwall/sjson"
	"gorm.io/gorm"
)

// / NewProviderOpenAICompatibility 创建一个新的 OpenAICompatibility 提供者。
func NewProviderOpenAICompatibility(db *gorm.DB) (Provider, error) {
	// 定义支持的模型及其功能。
	return &OpenAICompatibility{}, nil
}

// / init 注册提供者。
func init() {
	RegisterProvider(_const.ProviderOpenAICompatibility, NewProviderOpenAICompatibility)
}

// apiKeyCounters 存储每个模型的 API 密钥计数器
var apiKeyCounters sync.Map

// / OpenAICompatibility 实现了 OpenAI 模型的 Provider 接口。
type OpenAICompatibility struct {
	// baseUrl 是 API 的基础 URL。
	baseUrl string
	// baseUrlDirect 指示是否直接使用 baseUrl。
	baseUrlDirect bool
}

// getAPIKey 从模型的 API 密钥列表中选择一个 API 密钥
func (p *OpenAICompatibility) getAPIKey(model models.Model) string {
	if len(model.ProviderAPIKey) == 0 {
		return ""
	}

	// 获取模型的计数器
	counterValue, _ := apiKeyCounters.LoadOrStore(model.ID, int64(0))
	counter := counterValue.(int64)

	// 计算索引并获取 API 密钥
	index := int(counter) % len(model.ProviderAPIKey)
	apiKey := model.ProviderAPIKey[index]

	// 增加计数器
	apiKeyCounters.Store(model.ID, counter+1)

	log.Printf("Using API key: %s", apiKey)

	return apiKey
}

// / SetBaseUrl 设置 API 的基础 URL。
func (p *OpenAICompatibility) SetBaseUrl(url string, direct ...bool) {
	// 设置基础 URL。
	p.baseUrl = url
	// 如果提供了 direct 参数，则设置 baseUrlDirect。
	if len(direct) > 0 {
		p.baseUrlDirect = direct[0]
	}
}

// / GetProviderType 返回提供者的类型。
func (p *OpenAICompatibility) GetProviderType() _const.ProviderType {
	return _const.ProviderOpenAICompatibility
}

// / GetSupportedModels 返回此提供者支持的模型。
func (p *OpenAICompatibility) GetSupportedModels() map[string][]ModelType {
	return nil
}

// / CreateChatCompletion 创建一个聊天补全。
func (p *OpenAICompatibility) CreateChatCompletion(ctx context.Context, cancel context.CancelFunc, request []byte, model models.Model, usage *Usage) ([]byte, error, []byte) {
	// 如果模型仅支持流式传输，则使用流式传输。
	if model.StreamOnly {
		return p.CreateChatCompletionUseStream(ctx, cancel, request, model, usage)
	}

	// 定义 URL。
	var url string
	// 如果 baseUrlDirect 为 true，则直接使用 baseUrl。
	if p.baseUrlDirect {
		url = p.baseUrl
	} else {
		// 否则，构建聊天补全的 URL。
		url = fmt.Sprintf("%s/chat/completions", p.baseUrl)
	}

	// 创建 HTTP 请求。
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(request))
	// 如果创建请求失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	// 设置请求头。
	req.Header.Set("Content-Type", "application/json")
	// 获取 API 密钥并设置 Authorization 头。
	apiKey := p.getAPIKey(model)
	if apiKey != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))
	}

	// 使用 http.Client 发送请求。
	client := p.newHttpClient()
	resp, err := client.Do(req)
	// 如果发送请求失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	// 延迟关闭响应体。
	defer func() {
		err = resp.Body.Close()
	}()

	// 读取响应体。
	data, err := io.ReadAll(resp.Body)
	// 如果读取响应体失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	// 检查响应状态码。
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %s", resp.Status), data
	}

	// 检查响应是否包含 id 和 object 字段。
	responseIdResult := gjson.GetBytes(data, "id")
	responseObjectResult := gjson.GetBytes(data, "object")
	// 如果响应中没有 id 和 object 字段，则返回错误。
	if responseIdResult.Type == gjson.Null && responseObjectResult.Type == gjson.Null {
		return nil, fmt.Errorf("unexpected response: %s", string(data)), data
	}

	// 删除 provider 字段。
	data, _ = sjson.DeleteBytes(data, "provider")
	// 设置 model 字段。
	data, _ = sjson.SetBytes(data, "model", model.Name)

	// 如果响应中包含 usage 信息，则解析 usage 信息。
	if bytes.Contains(data, _const.TagPromptTokens) {
		// 解析 prompt_tokens。
		promptTokensResult := gjson.GetBytes(data, "usage.prompt_tokens")
		if promptTokensResult.Type == gjson.Number {
			usage.PromptTokens = int(promptTokensResult.Int())
		}

		// 解析 completion_tokens。
		completionTokensResult := gjson.GetBytes(data, "usage.completion_tokens")
		if completionTokensResult.Type == gjson.Number {
			usage.CompletionTokens = int(completionTokensResult.Int())
		}

		// 解析 total_tokens。
		totalTokensResult := gjson.GetBytes(data, "usage.total_tokens")
		if totalTokensResult.Type == gjson.Number {
			usage.TotalTokens = int(totalTokensResult.Int())
		}

		// 解析 cached_tokens。
		cachedTokensResult := gjson.GetBytes(data, "usage.prompt_tokens_details.cached_tokens")
		if cachedTokensResult.Type == gjson.Number {
			usage.PromptTokensDetails.CachedTokens = int(cachedTokensResult.Int())
		}

		// 解析 audio_tokens。
		promptAudioTokensResult := gjson.GetBytes(data, "usage.prompt_tokens_details.audio_tokens")
		if promptAudioTokensResult.Type == gjson.Number {
			usage.PromptTokensDetails.AudioTokens = int(promptAudioTokensResult.Int())
		}

		// 解析 audio_tokens。
		completionAudioTokensResult := gjson.GetBytes(data, "usage.completion_tokens_details.audio_tokens")
		if completionAudioTokensResult.Type == gjson.Number {
			usage.CompletionTokensDetails.AudioTokens = int(completionAudioTokensResult.Int())
		}

		// 解析 reasoning_tokens。
		completionReasoningTokensResult := gjson.GetBytes(data, "usage.completion_tokens_details.reasoning_tokens")
		if completionReasoningTokensResult.Type == gjson.Number {
			usage.CompletionTokensDetails.ReasoningTokens = int(completionReasoningTokensResult.Int())
		}
	}

	return data, err, nil
}

// / CreateChatCompletionStream 创建一个流式聊天补全。
func (p *OpenAICompatibility) CreateChatCompletionStream(ctx context.Context, cancel context.CancelFunc, request []byte, model models.Model, usage *Usage) (io.ReadCloser, error, []byte) {
	// 定义 URL。
	var url string
	// 如果 baseUrlDirect 为 true，则直接使用 baseUrl。
	if p.baseUrlDirect {
		url = p.baseUrl
	} else {
		// 否则，构建聊天补全的 URL。
		url = fmt.Sprintf("%s/chat/completions", p.baseUrl)
	}

	// 设置 stream_options.include_usage 为 true。
	request, err := sjson.SetBytes(request, "stream_options.include_usage", true)
	// 如果设置失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	// 创建 HTTP 请求。
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(request))
	// 如果创建请求失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	// 设置请求头。
	req.Header.Set("Content-Type", "application/json")
	// 获取 API 密钥并设置 Authorization 头。
	apiKey := p.getAPIKey(model)
	if apiKey != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))
	}
	// 设置 Accept 头为 text/event-stream。
	req.Header.Set("Accept", "text/event-stream")

	// 使用 http.Client 发送请求。
	client := p.newHttpClient()
	resp, err := client.Do(req)
	// 如果发送请求失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	// 检查响应状态码。
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("unexpected status code: %s", resp.Status), body
	}

	// 创建一个管道。
	pr, pw := io.Pipe()
	// 创建一个缓冲读取器。
	reader := bufio.NewReader(resp.Body)

	// 启动一个 goroutine 来处理流式响应。
	go func() {
		// 延迟关闭管道和响应体。
		defer func() {
			err = pw.Close()
			err = resp.Body.Close()
		}()

		// 循环读取流式响应。
		for {
			// 读取一行数据。
			var line []byte
			line, err = reader.ReadBytes('\n')
			// 如果读取失败，则处理错误。
			if err != nil {
				// 如果是 EOF，则表示读取完毕。
				if err == io.EOF {
					// 如果还有数据，则写入管道。
					if len(line) > 0 {
						_, err = pw.Write(bytes.TrimSpace(line))
					}
					break
				}
				// 如果是其他错误，则cancel请求。
				cancel()
				// 如果是其他错误，则关闭管道并返回错误。
				// _ = pw.CloseWithError(fmt.Errorf("error reading stream: %w", err))
				// _ = pr.CloseWithError(fmt.Errorf("stop writing stream: %w", err))
				return
			}

			// 去除行首尾的空格。
			line = bytes.TrimSpace(line)
			// 如果是空行，则跳过。
			if len(line) == 0 {
				continue
			}

			// 如果是以 data: 开头，则处理数据。
			if bytes.HasPrefix(line, _const.TagData) {
				// 去除 data: 前缀。
				data := bytes.TrimPrefix(line, _const.TagData)
				// 如果是 [DONE]，则表示流式响应结束。
				if bytes.Equal(data, _const.TagDataDone) {
					_, err = pw.Write([]byte("data: [DONE]\n\n"))
					err = pw.Close()
					break
				}

				// 删除 provider 字段。
				data, _ = sjson.DeleteBytes(data, "provider")
				// 设置 model 字段。
				data, _ = sjson.SetBytes(data, "model", model.Name)

				// 如果响应中包含 usage 信息，则解析 usage 信息。
				if bytes.Contains(data, _const.TagPromptTokens) {
					// 解析 prompt_tokens。
					promptTokensResult := gjson.GetBytes(data, "usage.prompt_tokens")
					if promptTokensResult.Type == gjson.Number {
						usage.PromptTokens = int(promptTokensResult.Int())
					}

					// 解析 completion_tokens。
					completionTokensResult := gjson.GetBytes(data, "usage.completion_tokens")
					if completionTokensResult.Type == gjson.Number {
						usage.CompletionTokens = int(completionTokensResult.Int())
					}

					// 解析 total_tokens。
					totalTokensResult := gjson.GetBytes(data, "usage.total_tokens")
					if totalTokensResult.Type == gjson.Number {
						usage.TotalTokens = int(totalTokensResult.Int())
					}

					// 解析 cached_tokens。
					cachedTokensResult := gjson.GetBytes(data, "usage.prompt_tokens_details.cached_tokens")
					if cachedTokensResult.Type == gjson.Number {
						usage.PromptTokensDetails.CachedTokens = int(cachedTokensResult.Int())
					}

					// 解析 audio_tokens。
					promptAudioTokensResult := gjson.GetBytes(data, "usage.prompt_tokens_details.audio_tokens")
					if promptAudioTokensResult.Type == gjson.Number {
						usage.PromptTokensDetails.AudioTokens = int(promptAudioTokensResult.Int())
					}

					// 解析 audio_tokens。
					completionAudioTokensResult := gjson.GetBytes(data, "usage.completion_tokens_details.audio_tokens")
					if completionAudioTokensResult.Type == gjson.Number {
						usage.CompletionTokensDetails.AudioTokens = int(completionAudioTokensResult.Int())
					}

					// 解析 reasoning_tokens。
					completionReasoningTokensResult := gjson.GetBytes(data, "usage.completion_tokens_details.reasoning_tokens")
					if completionReasoningTokensResult.Type == gjson.Number {
						usage.CompletionTokensDetails.ReasoningTokens = int(completionReasoningTokensResult.Int())
					}
				}

				// 构造输出数据。
				output := []byte("data: ")
				output = append(output, data...)
				output = append(output, []byte("\n\n")...)
				// 将数据写入管道。
				_, err = pw.Write(output)
			}
		}
	}()

	return pr, nil, nil
}

// / CreateChatCompletionUseStream 使用流式传输创建一个聊天补全。
func (p *OpenAICompatibility) CreateChatCompletionUseStream(ctx context.Context, cancel context.CancelFunc, request []byte, model models.Model, usage *Usage) ([]byte, error, []byte) {
	// 设置 stream 为 true，以便在内部使用流式 API。
	streamRequest, err := json.Marshal(map[string]interface{}{
		"stream": true,
	})
	// 如果序列化失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	// 将原始请求与 stream 标志合并。
	request, err = jsonMerge(request, streamRequest)
	// 如果合并失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	// 调用流式 API。
	stream, err, errBody := p.CreateChatCompletionStream(ctx, cancel, request, model, usage)
	// 如果调用失败，则返回错误。
	if err != nil {
		return nil, err, errBody
	}
	// 延迟关闭流。
	defer func() {
		err = stream.Close()
	}()

	// 从流中读取所有块。
	reader := bufio.NewReader(stream)
	// 定义完整响应。
	var fullResponse *ChatCompletionResponse
	// 定义内容。
	var content string
	// 定义工具调用。
	var toolCalls []map[string]interface{}

	// 循环读取流。
	for {
		// 读取一行数据。
		var line []byte
		line, err = reader.ReadBytes('\n')
		// 如果读取失败，则处理错误。
		if err != nil {
			// 如果是 EOF，则表示读取完毕。
			if err == io.EOF {
				break
			}
			return nil, err, nil
		}

		// 去除行首尾的空格。
		line = bytes.TrimSpace(line)
		// 如果是空行，则跳过。
		if len(line) == 0 {
			continue
		}

		// 检查是否是数据行。
		if bytes.HasPrefix(line, []byte("data: ")) {
			// 去除 data: 前缀。
			data := bytes.TrimPrefix(line, []byte("data: "))

			// 检查是否是 [DONE] 标记。
			if bytes.Equal(data, []byte("[DONE]")) {
				break
			}

			// 解析块。
			var chunk map[string]interface{}
			if err = json.Unmarshal(data, &chunk); err != nil {
				continue
			}

			// 如果尚未初始化，则初始化完整响应。
			if fullResponse == nil {
				fullResponse = &ChatCompletionResponse{
					ID:                chunk["id"].(string),
					Object:            "chat.completion",
					Created:           int64(chunk["created"].(float64)),
					Model:             chunk["model"].(string),
					SystemFingerprint: "",
					Choices:           []ChatCompletionChoice{},
					Usage:             Usage{},
				}

				// 检查 system_fingerprint 是否存在。
				if fingerprint, ok := chunk["system_fingerprint"]; ok {
					fullResponse.SystemFingerprint = fingerprint.(string)
				}
			}

			// 从块中提取内容和工具调用。
			if choices, ok := chunk["choices"].([]interface{}); ok && len(choices) > 0 {
				choice := choices[0].(map[string]interface{})

				// 如果存在 delta，则处理 delta。
				if delta, deltaOK := choice["delta"].(map[string]interface{}); deltaOK {
					// 追加内容。
					if deltaContent, deltaContentOK := delta["content"].(string); deltaContentOK && deltaContent != "" {
						content += deltaContent
					}

					// 处理工具调用。
					if deltaToolCalls, deltaToolCallsOK := delta["tool_calls"].([]interface{}); deltaToolCallsOK && len(deltaToolCalls) > 0 {
						// 遍历工具调用。
						for _, tc := range deltaToolCalls {
							toolCall := tc.(map[string]interface{})

							// 查找具有相同索引的现有工具调用或创建一个新的工具调用。
							var existingToolCall map[string]interface{}
							toolCallIndex := int(toolCall["index"].(float64))

							// 确保 toolCalls 长度足够。
							for len(toolCalls) <= toolCallIndex {
								toolCalls = append(toolCalls, map[string]interface{}{
									"index": len(toolCalls),
									"id":    "",
									"type":  "",
									"function": map[string]interface{}{
										"name":      "",
										"arguments": "",
									},
								})
							}

							existingToolCall = toolCalls[toolCallIndex]

							// 更新工具调用属性。
							if id, idOK := toolCall["id"].(string); idOK && id != "" {
								existingToolCall["id"] = id
							}

							if tcType, tcTypeOK := toolCall["type"].(string); tcTypeOK && tcType != "" {
								existingToolCall["type"] = tcType
							}

							if function, functionOK := toolCall["function"].(map[string]interface{}); functionOK {
								existingFunc := existingToolCall["function"].(map[string]interface{})

								if name, nameOK := function["name"].(string); nameOK && name != "" {
									existingFunc["name"] = name
								}

								if args, argsOK := function["arguments"].(string); argsOK {
									existingArgs := existingFunc["arguments"].(string)
									existingFunc["arguments"] = existingArgs + args
								}
							}
						}
					}

					// 如果可用，则获取完成原因。
					if finishReason, finishReasonOK := choice["finish_reason"].(string); finishReasonOK && finishReason != "" {
						if len(fullResponse.Choices) == 0 {
							fullResponse.Choices = append(fullResponse.Choices, ChatCompletionChoice{
								Index:        0,
								FinishReason: finishReason,
							})
						} else {
							fullResponse.Choices[0].FinishReason = finishReason
						}
					}
				}
			}

			// 如果可用，则提取使用信息。
			if usageData, ok := chunk["usage"].(map[string]interface{}); ok {
				if promptTokens, promptTokensOK := usageData["prompt_tokens"].(float64); promptTokensOK {
					usage.PromptTokens = int(promptTokens)
				}

				if completionTokens, completionTokensOK := usageData["completion_tokens"].(float64); completionTokensOK {
					usage.CompletionTokens = int(completionTokens)
				}

				if totalTokens, totalTokensOK := usageData["total_tokens"].(float64); totalTokensOK {
					usage.TotalTokens = int(totalTokens)
				}

				// 处理 prompt tokens 详细信息。
				if promptTokensDetails, promptTokensDetailsOK := usageData["prompt_tokens_details"].(map[string]interface{}); promptTokensDetailsOK {
					if cachedTokens, cachedTokensOK := promptTokensDetails["cached_tokens"].(float64); cachedTokensOK {
						usage.PromptTokensDetails.CachedTokens = int(cachedTokens)
					}

					if audioTokens, audioTokensOK := promptTokensDetails["audio_tokens"].(float64); audioTokensOK {
						usage.PromptTokensDetails.AudioTokens = int(audioTokens)
					}
				}

				// 处理 completion tokens 详细信息。
				if completionTokensDetails, completionTokensDetailsOK := usageData["completion_tokens_details"].(map[string]interface{}); completionTokensDetailsOK {
					if audioTokens, audioTokensOK := completionTokensDetails["audio_tokens"].(float64); audioTokensOK {
						usage.CompletionTokensDetails.AudioTokens = int(audioTokens)
					}

					if reasoningTokens, reasoningTokensOK := completionTokensDetails["reasoning_tokens"].(float64); reasoningTokensOK {
						usage.CompletionTokensDetails.ReasoningTokens = int(reasoningTokens)
					}
				}
			}
		}
	}

	// 如果我们没有得到有效的响应，则返回错误。
	if fullResponse == nil {
		return nil, errors.New("failed to parse streaming response"), nil
	}

	// 使用内容和工具调用创建最终消息。
	message := Message{
		Role:    "assistant",
		Content: content,
	}

	// 如果有工具调用，则添加工具调用。
	if len(toolCalls) > 0 {
		// 将工具调用转换为响应中预期的格式。
		messageToolCalls := make([]map[string]interface{}, len(toolCalls))
		for i, tc := range toolCalls {
			messageToolCalls[i] = tc
		}

		// 将工具调用添加到消息元数据。
		message.Meta = map[string]interface{}{
			"tool_calls": messageToolCalls,
		}
	}

	// 在响应中设置消息。
	if len(fullResponse.Choices) == 0 {
		fullResponse.Choices = append(fullResponse.Choices, ChatCompletionChoice{
			Index:   0,
			Message: message,
		})
	} else {
		fullResponse.Choices[0].Message = message
	}

	// 设置使用信息。
	fullResponse.Usage = *usage

	// 将响应转换为 JSON。
	responseBytes, err := json.Marshal(fullResponse)
	// 如果转换失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	return responseBytes, nil, nil
}

// / CreateImage 创建一个图片。
func (p *OpenAICompatibility) CreateImage(ctx context.Context, model models.Model, request ImageGenerationRequest) (*ImageResponse, error, []byte) {
	return nil, errors.New("image generation not supported by OpenAI provider"), nil
}

// / ProcessFile 处理文件上传。
func (p *OpenAICompatibility) ProcessFile(ctx context.Context, file *multipart.FileHeader, purpose string) (string, error) {
	return "", errors.New("file processing not implemented for OpenAI provider")
}

// / Close 关闭提供者。
func (p *OpenAICompatibility) Close() error {
	return nil
}

// / newHttpClient 创建一个新的 HTTP 客户端。
func (p *OpenAICompatibility) newHttpClient() *http.Client {
	// 创建一个 HTTP 传输。
	transport := &http.Transport{
		// 设置连接上下文。
		DialContext: (&net.Dialer{
			// 设置连接超时为 3 秒。
			Timeout: 3 * time.Second,
			// 设置保持连接存活。
			KeepAlive: 30 * time.Second,
		}).DialContext,
	}

	// 创建 HTTP 客户端，不设置全局超时。
	return &http.Client{
		// 设置传输。
		Transport: transport,
	}
}
