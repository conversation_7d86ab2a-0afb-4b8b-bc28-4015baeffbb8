package provider

//
// import (
// 	"context"
// 	"encoding/json"
// 	"errors"
// 	_const "github.com/luispater/ai-router/const"
// 	"github.com/luispater/ai-router/models"
// 	"github.com/tidwall/sjson"
// 	"gorm.io/gorm"
// 	"io"
// 	"mime/multipart"
// )
//
// // jsonMerge merges two JSON objects using sjson
// func jsonMerge(original, patch []byte) ([]byte, error) {
// 	// Parse the patch JSON
// 	var patchObj map[string]interface{}
// 	if err := json.Unmarshal(patch, &patchObj); err != nil {
// 		return nil, err
// 	}
//
// 	// Apply each field from the patch to the original
// 	result := original
// 	var err error
// 	for key, value := range patchObj {
// 		result, err = sjson.SetBytes(result, key, value)
// 		if err != nil {
// 			return nil, err
// 		}
// 	}
//
// 	return result, nil
// }
//
// // GliderProvider implements the Provider interface for OpenAI models
// type GliderProvider struct {
// 	OpenAICompatibility
// 	models map[string][]ModelType
// 	db     *gorm.DB
// }
//
// // NewGliderProvider creates a new OpenAI provider
// func NewGliderProvider(db *gorm.DB) (Provider, error) {
// 	// Define supported models and their capabilities
// 	m := map[string][]ModelType{}
//
// 	var providerModels []models.Model
// 	result := db.Where("provider_id=?", _const.ProviderGlider).Find(&providerModels)
// 	if result.Error != nil {
// 		return nil, result.Error
// 	}
//
// 	for _, model := range providerModels {
// 		var capabilities []ModelType
//
// 		if model.SupportsChat {
// 			capabilities = append(capabilities, ModelTypeChat)
// 		}
// 		if model.SupportsCompletion {
// 			capabilities = append(capabilities, ModelTypeCompletion)
// 		}
// 		if model.SupportsEmbedding {
// 			capabilities = append(capabilities, ModelTypeEmbedding)
// 		}
// 		if model.SupportsImageGen {
// 			capabilities = append(capabilities, ModelTypeImageGen)
// 		}
// 		if model.SupportsImageEdit {
// 			capabilities = append(capabilities, ModelTypeImageEdit)
// 		}
// 		if model.SupportsImageVar {
// 			capabilities = append(capabilities, ModelTypeImageVar)
// 		}
// 		if model.SupportsAudioTrans {
// 			capabilities = append(capabilities, ModelTypeAudioTrans)
// 		}
// 		if model.SupportsAudioTrans2 {
// 			capabilities = append(capabilities, ModelTypeAudioTrans2)
// 		}
//
// 		m[model.Name] = capabilities
// 	}
//
// 	return &GliderProvider{
// 		models: m,
// 		db:     db,
// 	}, nil
// }
//
// // Register the provider
// func init() {
// 	RegisterProvider(_const.ProviderGlider, NewGliderProvider)
// }
//
// // GetProviderType returns the type of the provider
// func (p *GliderProvider) GetProviderType() _const.ProviderType {
// 	return _const.ProviderGlider
// }
//
// // GetSupportedModels returns the models supported by this provider
// func (p *GliderProvider) GetSupportedModels() map[string][]ModelType {
// 	return p.models
// }
//
// // CreateChatCompletion creates a chat completion
// func (p *GliderProvider) CreateChatCompletion(ctx context.Context, request []byte, model models.Model, usage *Usage) ([]byte, error) {
// 	p.SetBaseUrl("https://glider.so/api/chat", true)
// 	return p.CreateChatCompletionUseStream(ctx, request, model, usage)
// }
//
// // CreateChatCompletionStream creates a streaming chat completion
// func (p *GliderProvider) CreateChatCompletionStream(ctx context.Context, request []byte, model models.Model, usage *Usage) (io.ReadCloser, error) {
// 	p.SetBaseUrl("https://glider.so/api/chat", true)
// 	return p.OpenAICompatibility.CreateChatCompletionStream(ctx, request, model, usage)
// }
//
// // CreateImage generates images based on a prompt
// func (p *GliderProvider) CreateImage(ctx context.Context, request ImageGenerationRequest) (*ImageResponse, error) {
// 	return nil, errors.New("image generation not supported by Glider provider")
// }
//
// // ProcessFile handles file uploads
// func (p *GliderProvider) ProcessFile(ctx context.Context, file *multipart.FileHeader, purpose string) (string, error) {
// 	return "", errors.New("file processing not implemented for Glider provider")
// }
//
// // Close closes any resources used by the provider
// func (p *GliderProvider) Close() error {
// 	return nil
// }
