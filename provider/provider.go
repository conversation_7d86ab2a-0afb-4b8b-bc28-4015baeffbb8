package provider

import (
	"context"
	"encoding/json"
	"io"
	"mime/multipart"

	_const "github.com/luispater/ai-router/const"
	"github.com/luispater/ai-router/models"
	"github.com/openai/openai-go"
	"github.com/tidwall/sjson"
	"gorm.io/gorm"
)

// ModelType 表示模型的类型
type ModelType string

const (
	// ModelTypeChat 表示聊天模型
	ModelTypeChat ModelType = "chat"
	// ModelTypeCompletion 表示补全模型
	ModelTypeCompletion ModelType = "completion"
	// ModelTypeEmbedding 表示嵌入模型
	ModelTypeEmbedding ModelType = "embedding"
	// ModelTypeImageGen 表示图像生成模型
	ModelTypeImageGen ModelType = "image_generation"
	// ModelTypeImageEdit 表示图像编辑模型
	ModelTypeImageEdit ModelType = "image_edit"
	// ModelTypeImageVar 表示图像变体模型
	ModelTypeImageVar ModelType = "image_variation"
	// ModelTypeAudioTrans 表示音频转录模型
	ModelTypeAudioTrans ModelType = "audio_transcription"
	// ModelTypeAudioTrans2 表示音频翻译模型
	ModelTypeAudioTrans2 ModelType = "audio_translation"
)

// ChatCompletionRequestUnion 聊天补全请求联合体
type ChatCompletionRequestUnion struct {
	// StringContent 字符串内容
	StringContent *ChatCompletionRequest
	// ArrayContent 数组内容
	ArrayContent *ChatCompletionRequestArrayContent
}

// Message 表示聊天消息
type Message struct {
	// Role 角色
	Role string `json:"role"`
	// Content 内容
	Content string `json:"content"`
	// Images 图片数据，用于多模态模型
	Images [][]byte `json:"-"`
	// Name 消息发送者的名称
	Name string `json:"name,omitempty"`
	// Meta 元数据
	Meta map[string]interface{} `json:"-"`
}

// ChatCompletionRequest 表示聊天补全的请求
type ChatCompletionRequest struct {
	// Model 使用的模型
	Model string `json:"model"`
	// Messages 消息列表
	Messages []Message `json:"messages"`
	// MaxTokens 最大令牌数
	MaxTokens int `json:"max_tokens,omitempty"`
	// Temperature 温度
	Temperature float64 `json:"temperature,omitempty"`
	// TopP 核心采样
	TopP float64 `json:"top_p,omitempty"`
	// N 生成的回复数量
	N int `json:"n,omitempty"`
	// Stream 是否流式传输
	Stream bool `json:"stream,omitempty"`
	// Stop 停止词
	Stop []string `json:"stop,omitempty"`
	// PresencePenalty 存在惩罚
	PresencePenalty float64 `json:"presence_penalty,omitempty"`
	// FrequencyPenalty 频率惩罚
	FrequencyPenalty float64 `json:"frequency_penalty,omitempty"`
	// User 用户标识
	User string `json:"user,omitempty"`
	// Functions 函数列表
	Functions []map[string]any `json:"functions,omitempty"`
	// FunctionCall 函数调用
	FunctionCall any `json:"function_call,omitempty"`
	// ResponseFormat 响应格式
	ResponseFormat map[string]interface{} `json:"response_format,omitempty"`
	// Seed 种子
	Seed int `json:"seed,omitempty"`
	// Tools 工具列表
	Tools []map[string]any `json:"tools,omitempty"`
	// ToolChoice 工具选择
	ToolChoice any `json:"tool_choice,omitempty"`
}

// MessageArrayContentItemImageURL 消息数组内容项图片URL
type MessageArrayContentItemImageURL struct {
	// URL 图片URL
	URL string `json:"url,omitempty"`
}

// MessageArrayContentItem 消息数组内容项
type MessageArrayContentItem struct {
	// Type 类型
	Type string `json:"type"`
	// Text 文本内容
	Text string `json:"text,omitempty"`
	// ImageURL 图片URL
	ImageURL MessageArrayContentItemImageURL `json:"image_url,omitempty"`
}

// MessageArrayContent 消息数组内容
type MessageArrayContent struct {
	// Role 角色
	Role string `json:"role"`
	// Content 内容列表
	Content []MessageArrayContentItem `json:"content"`
	// Name 消息发送者的名称
	Name string `json:"name,omitempty"`
}

// ChatCompletionRequestArrayContent 聊天补全请求数组内容
type ChatCompletionRequestArrayContent struct {
	// Model 使用的模型
	Model string `json:"model"`
	// Messages 消息列表
	Messages []MessageArrayContent `json:"messages"`
	// MaxTokens 最大令牌数
	MaxTokens int `json:"max_tokens,omitempty"`
	// Temperature 温度
	Temperature float64 `json:"temperature,omitempty"`
	// TopP 核心采样
	TopP float64 `json:"top_p,omitempty"`
	// N 生成的回复数量
	N int `json:"n,omitempty"`
	// Stream 是否流式传输
	Stream bool `json:"stream,omitempty"`
	// Stop 停止词
	Stop []string `json:"stop,omitempty"`
	// PresencePenalty 存在惩罚
	PresencePenalty float64 `json:"presence_penalty,omitempty"`
	// FrequencyPenalty 频率惩罚
	FrequencyPenalty float64 `json:"frequency_penalty,omitempty"`
	// User 用户标识
	User string `json:"user,omitempty"`
	// Functions 函数列表
	Functions []map[string]any `json:"functions,omitempty"`
	// FunctionCall 函数调用
	FunctionCall any `json:"function_call,omitempty"`
	// ResponseFormat 响应格式
	ResponseFormat map[string]interface{} `json:"response_format,omitempty"`
	// Seed 种子
	Seed int `json:"seed,omitempty"`
	// Tools 工具列表
	Tools []map[string]any `json:"tools,omitempty"`
	// ToolChoice 工具选择
	ToolChoice any `json:"tool_choice,omitempty"`
}

// ChatCompletionResponse 表示聊天补全的响应
type ChatCompletionResponse struct {
	// ID 响应ID
	ID string `json:"id"`
	// Object 对象类型
	Object string `json:"object"`
	// Created 创建时间
	Created int64 `json:"created"`
	// Model 使用的模型
	Model string `json:"model"`
	// SystemFingerprint 系统指纹
	SystemFingerprint string `json:"system_fingerprint,omitempty"`
	// Choices 选项列表
	Choices []ChatCompletionChoice `json:"choices"`
	// Usage 使用信息
	Usage Usage `json:"usage"`
}

// ChatCompletionChoice 表示聊天补全响应中的一个选项
type ChatCompletionChoice struct {
	// Index 选项索引
	Index int `json:"index"`
	// Message 消息内容
	Message Message `json:"message"`
	// FinishReason 完成原因
	FinishReason string `json:"finish_reason"`
	// LogProbs 日志概率
	LogProbs map[string]interface{} `json:"logprobs,omitempty"`
}

// Usage 表示令牌使用信息
type Usage struct {
	// PromptTokens 提示令牌数
	PromptTokens int `json:"prompt_tokens"`
	// CompletionTokens 完成令牌数
	CompletionTokens int `json:"completion_tokens"`
	// TotalTokens 总令牌数
	TotalTokens int `json:"total_tokens"`
	// PromptTokensDetails 提示令牌详细信息
	PromptTokensDetails UsagePromptTokensDetails `json:"prompt_tokens_details"`
	// CompletionTokensDetails 完成令牌详细信息
	CompletionTokensDetails UsageCompletionTokensDetails `json:"completion_tokens_details"`
}

// UsagePromptTokensDetails 提示令牌详细信息
type UsagePromptTokensDetails struct {
	// CachedTokens 缓存令牌数
	CachedTokens int `json:"cached_tokens"`
	// AudioTokens 音频令牌数
	AudioTokens int `json:"audio_tokens"`
}

// UsageCompletionTokensDetails 完成令牌详细信息
type UsageCompletionTokensDetails struct {
	// AudioTokens 音频令牌数
	AudioTokens int `json:"audio_tokens"`
	// ReasoningTokens 推理令牌数
	ReasoningTokens int `json:"reasoning_tokens"`
}

// ImageGenerationRequest 表示图像生成的请求
type ImageGenerationRequest struct {
	// Prompt 提示词
	Prompt string `json:"prompt"`
	// NegativePrompt 负面提示词
	NegativePrompt string `json:"negative_prompt"`
	// Model 使用的模型
	Model string `json:"model,omitempty"`
	// N 生成的图像数量
	N int `json:"n,omitempty"`
	// Size 图像尺寸
	Size string `json:"size,omitempty"`
	// Quality 图像质量
	Quality string `json:"quality,omitempty"`
	// ResponseFormat 响应格式
	ResponseFormat string `json:"response_format,omitempty"`
	// Style 图像风格
	Style string `json:"style,omitempty"`
	// User 用户标识
	User string `json:"user,omitempty"`
	// Seed 种子
	Seed int64 `json:"seed,omitempty"`
	// Temperature 温度
	Temperature float64 `json:"temperature,omitempty"`
	// Guidance Scale 文本指导
	GuidanceScale float64 `json:"guidance_scale,omitempty"`
	// Steps 推理步数
	Steps int `json:"steps,omitempty"`
}

// ImageResponse 表示图像生成的响应
type ImageResponse struct {
	// Created 创建时间
	Created int64 `json:"created"`
	// Data 图像数据列表
	Data []ImageObject `json:"data"`
}

// ImageObject 表示响应中的一个图像对象
type ImageObject struct {
	// URL 图像URL
	URL string `json:"url,omitempty"`
	// B64JSON Base64编码的JSON数据
	B64JSON string `json:"b64_json,omitempty"`
	// Seed 种子
	Seed int64 `json:"seed,omitempty"`
}

// Provider 定义了所有 AI 提供者必须实现的接口
type Provider interface {
	// / GetProviderType 返回提供者的类型
	GetProviderType() _const.ProviderType

	// / GetSupportedModels 返回此提供者支持的模型
	GetSupportedModels() map[string][]ModelType

	// / CreateChatCompletion 创建一个聊天补全
	CreateChatCompletion(ctx context.Context, cancel context.CancelFunc, request []byte, model models.Model, usage *Usage) ([]byte, error, []byte)

	// / CreateChatCompletionStream 创建一个流式聊天补全
	CreateChatCompletionStream(ctx context.Context, cancel context.CancelFunc, request []byte, model models.Model, usage *Usage) (io.ReadCloser, error, []byte)

	// / CreateImage 根据提示生成图像
	CreateImage(ctx context.Context, model models.Model, request ImageGenerationRequest) (*ImageResponse, error, []byte)

	// / ProcessFile 处理文件上传（用于微调等）
	ProcessFile(ctx context.Context, file *multipart.FileHeader, purpose string) (string, error)

	// / Close 关闭提供者使用的任何资源
	Close() error
}

// ProviderFactory 是一个创建新的提供者实例的函数
type ProviderFactory func(db *gorm.DB) (Provider, error)

// ProviderRegistry 存储所有可用的提供者
var ProviderRegistry = make(map[_const.ProviderType]ProviderFactory)

// RegisterProvider 注册一个新的提供者工厂
func RegisterProvider(providerType _const.ProviderType, factory ProviderFactory) {
	ProviderRegistry[providerType] = factory
}

// Errors 错误定义
var (
	// ErrProviderNotFound 提供者未找到
	ErrProviderNotFound = NewProviderError("provider not found")
	// ErrInvalidRequest 无效的请求
	ErrInvalidRequest = NewProviderError("invalid request")
	// ErrRateLimited 速率限制
	ErrRateLimited = NewProviderError("rate limited")
	// ErrUnauthorized 未授权
	ErrUnauthorized = NewProviderError("unauthorized")
)

// ProviderError 表示来自提供者的错误
type ProviderError struct {
	// Message 错误消息
	Message string
}

// Error 实现了 error 接口
func (e ProviderError) Error() string {
	return e.Message
}

// NewProviderError 创建一个新的提供者错误
func NewProviderError(message string) ProviderError {
	return ProviderError{Message: message}
}

// convertMessageToOpenAI 将消息转换为 OpenAI 格式
func convertMessageToOpenAI(role, message string) openai.ChatCompletionMessageParamUnion {
	// 根据角色类型创建不同的消息类型
	switch role {
	case "system":
		// 系统消息
		return openai.SystemMessage(message)
	case "assistant":
		// 助手消息
		return openai.AssistantMessage(message)
	case "user":
		// 用户消息
		return openai.UserMessage(message)
	default:
		// 默认用户消息
		return openai.UserMessage(message)
	}
}

// convertMessages 将消息列表转换为 OpenAI 格式
func convertMessages(input []MessageArrayContent) ([]openai.ChatCompletionMessageParamUnion, error) {
	// 结果列表
	var result []openai.ChatCompletionMessageParamUnion

	// 遍历输入的消息列表
	for _, m := range input {
		// 根据角色类型处理消息
		switch m.Role {
		case "system":
			// 系统消息
			var parts []openai.ChatCompletionContentPartTextParam

			// 遍历内容列表
			for _, c := range m.Content {
				// 如果有文本内容，则添加到 parts 中
				if c.Text != "" {
					parts = append(parts, openai.ChatCompletionContentPartTextParam{Text: c.Text})
				}
			}
			// 创建系统消息
			message := openai.SystemMessage(parts)
			// 添加到结果列表
			result = append(result, message)
		case "user":
			// 用户消息
			var parts []openai.ChatCompletionContentPartUnionParam

			// 遍历内容列表
			for _, c := range m.Content {
				// 如果有文本内容，则添加到 parts 中
				if c.Text != "" {
					parts = append(parts, openai.TextContentPart(c.Text))
				}

				// 如果有图片URL，则添加到 parts 中
				if c.ImageURL.URL != "" {
					imageURL := openai.ChatCompletionContentPartImageImageURLParam{
						URL: c.ImageURL.URL,
					}

					part := openai.ImageContentPart(imageURL)
					parts = append(parts, part)
				}
			}
			// 创建用户消息
			result = append(result, openai.UserMessage(parts))
		case "assistant":
			// 助手消息
			message := openai.ChatCompletionAssistantMessageParam{}

			// 内容列表
			var content []openai.ChatCompletionAssistantMessageParamContentArrayOfContentPartUnion

			// 遍历内容列表
			for _, c := range m.Content {
				// 如果有文本内容，则添加到 content 中
				if c.Text != "" {
					content = append(content, openai.ChatCompletionAssistantMessageParamContentArrayOfContentPartUnion{
						OfText: &openai.ChatCompletionContentPartTextParam{
							Text: c.Text,
						},
					})
				}
			}

			// 如果有内容，则设置消息内容
			if len(content) > 0 {
				message.Content.OfArrayOfContentParts = content
			}

			// 添加到结果列表
			result = append(result, openai.ChatCompletionMessageParamUnion{OfAssistant: &message})
		}
	}

	return result, nil
}

// jsonMerge 合并两个 JSON 对象
func jsonMerge(original, patch []byte) ([]byte, error) {
	// 解析 patch JSON
	var patchObj map[string]interface{}
	if err := json.Unmarshal(patch, &patchObj); err != nil {
		return nil, err
	}

	// 将 patch 中的每个字段应用到 original 中
	result := original
	var err error
	for key, value := range patchObj {
		result, err = sjson.SetBytes(result, key, value)
		if err != nil {
			return nil, err
		}
	}

	return result, nil
}
