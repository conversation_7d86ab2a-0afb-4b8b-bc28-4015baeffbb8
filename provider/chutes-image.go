package provider

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	_const "github.com/luispater/ai-router/const"
	"github.com/luispater/ai-router/core"
	"github.com/luispater/ai-router/models"
	"github.com/tidwall/gjson"
	"gorm.io/gorm"
	"image/jpeg"
	"image/png"
	"io"
	"mime/multipart"
	"net"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"
)

// ChutesProvider implements the Provider interface for OpenAI models
type ChutesImageProvider struct {
	db             *gorm.DB
	apiKeyCounters sync.Map
}

// NewChutesProvider creates a new OpenAI provider
func NewChutesImageProvider(db *gorm.DB) (Provider, error) {
	// Define supported models and their capabilities
	m := map[string][]ModelType{}

	var providerModels []models.Model
	result := db.Where("provider_id=?", _const.ProviderChutes).Find(&providerModels)
	if result.Error != nil {
		return nil, result.Error
	}

	for _, model := range providerModels {
		var capabilities []ModelType

		if model.SupportsChat {
			capabilities = append(capabilities, ModelTypeChat)
		}
		if model.SupportsCompletion {
			capabilities = append(capabilities, ModelTypeCompletion)
		}
		if model.SupportsEmbedding {
			capabilities = append(capabilities, ModelTypeEmbedding)
		}
		if model.SupportsImageGen {
			capabilities = append(capabilities, ModelTypeImageGen)
		}
		if model.SupportsImageEdit {
			capabilities = append(capabilities, ModelTypeImageEdit)
		}
		if model.SupportsImageVar {
			capabilities = append(capabilities, ModelTypeImageVar)
		}
		if model.SupportsAudioTrans {
			capabilities = append(capabilities, ModelTypeAudioTrans)
		}
		if model.SupportsAudioTrans2 {
			capabilities = append(capabilities, ModelTypeAudioTrans2)
		}

		m[model.Name] = capabilities
	}

	return &ChutesImageProvider{
		db: db,
	}, nil
}

// Register the provider
func init() {
	RegisterProvider(_const.ProviderChutes, NewChutesImageProvider)
}

// GetProviderType returns the type of the provider
func (p *ChutesImageProvider) GetProviderType() _const.ProviderType {
	return _const.ProviderChutes
}

// GetSupportedModels returns the models supported by this provider
func (p *ChutesImageProvider) GetSupportedModels() map[string][]ModelType {
	return nil
}

// CreateChatCompletion creates a chat completion
func (p *ChutesImageProvider) CreateChatCompletion(ctx context.Context, cancel context.CancelFunc, request []byte, model models.Model, usage *Usage) ([]byte, error, []byte) {
	return nil, errors.New("chat completion not supported by Chutes Stable Flow provider"), nil
}

// CreateChatCompletionStream creates a streaming chat completion
func (p *ChutesImageProvider) CreateChatCompletionStream(ctx context.Context, cancel context.CancelFunc, request []byte, model models.Model, usage *Usage) (io.ReadCloser, error, []byte) {
	return nil, errors.New("chat completion not supported by Chutes Stable Flow provider"), nil
}

// CreateImage generates images based on a prompt
func (p *ChutesImageProvider) CreateImage(ctx context.Context, model models.Model, request ImageGenerationRequest) (*ImageResponse, error, []byte) {
	generateImageURL := ""
	mapRequest := make(map[string]interface{})

	if request.Model == "stable-flow" {
		generateImageURL = "https://chutes-stable-flow.chutes.ai/generate"
		mapRequest["prompt"] = request.Prompt
		mapRequest["edit_prompts"] = []string{""}
		mapRequest["guidance_scale"] = request.GuidanceScale
		mapRequest["num_inference_steps"] = request.Steps
		mapRequest["seed"] = request.Seed
	} else if request.Model == "chroma" {
		mapRequest["prompt"] = request.Prompt
		generateImageURL = "https://chutes-chroma.chutes.ai/generate"
		mapRequest["cfg"] = request.GuidanceScale
		mapRequest["num_inference_steps"] = request.Steps
		mapRequest["seed"] = request.Seed

		splitSize := strings.Split(request.Size, "x")
		if len(splitSize) != 2 {
			return nil, errors.New("invalid size format"), nil
		}
		width, err := strconv.Atoi(strings.Split(request.Size, "x")[0])
		if err != nil {
			return nil, fmt.Errorf("invalid width format: %v", err), nil
		}

		height, err := strconv.ParseInt(strings.Split(request.Size, "x")[1], 10, 64)
		if err != nil {
			return nil, fmt.Errorf("invalid height format: %v", err), nil
		}
		mapRequest["width"] = width
		mapRequest["height"] = height
	} else if request.Model == "hidream" {
		mapRequest["prompt"] = request.Prompt
		generateImageURL = "https://chutes-hidream.chutes.ai/generate"
		mapRequest["guidance_scale"] = request.GuidanceScale
		mapRequest["num_inference_steps"] = request.Steps
		mapRequest["seed"] = request.Seed
		supportSize := []string{"1024x1024", "768x1360", "1360x768", "880x1168", "1168x880", "1248x832", "832x1248"}
		if !core.InArray(request.Size, supportSize) {
			return nil, errors.New(fmt.Sprintf("size %s not supported by current provider", request.Size)), nil
		} else {
			mapRequest["resolution"] = request.Size
		}
	} else {
		return nil, errors.New(fmt.Sprintf("model %s not supported by current provider", request.Model)), nil
	}

	bodyRequest, err := json.Marshal(mapRequest)
	if err != nil {
		return nil, err, nil
	}

	req, err := http.NewRequestWithContext(ctx, "POST", generateImageURL, bytes.NewBuffer(bodyRequest))
	// 如果创建请求失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	// 设置请求头。
	req.Header.Set("Content-Type", "application/json")
	// 获取 API 密钥并设置 Authorization 头。
	apiKey := p.getAPIKey(model)
	if apiKey != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))
	}

	// 使用 http.Client 发送请求。
	client := p.newHttpClient()
	resp, err := client.Do(req)
	// 如果发送请求失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	// 延迟关闭响应体。
	defer func() {
		err = resp.Body.Close()
	}()

	// 读取响应体。
	data, err := io.ReadAll(resp.Body)
	// 如果读取响应体失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	// 检查响应状态码。
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %s", resp.Status), data
	}

	imageResponse := new(ImageResponse)
	imageResponse.Created = time.Now().Unix()

	if request.Model == "stable-flow" {
		// 将 base64 jpg 转换为 png
		result := gjson.GetBytes(data, "0")
		if result.Type != gjson.String {
			return nil, fmt.Errorf("image data format error"), nil
		}

		data = []byte(result.String())
		// 确保数据是有效的 base64
		if len(data)%4 != 0 {
			padding := 4 - (len(data) % 4)
			data = append(data, bytes.Repeat([]byte("="), padding)...)
		}

		var jpgData []byte
		jpgData, err = base64.StdEncoding.DecodeString(string(data))
		if err != nil {
			return nil, fmt.Errorf("failed to decode BASE64 to JPEG: %v", err), nil
		}

		// 解码 JPEG
		jpegReader := bytes.NewReader(jpgData)
		img, errDecode := jpeg.Decode(jpegReader)
		if errDecode != nil {
			return nil, fmt.Errorf("failed to decode JPEG: %v", errDecode), nil
		}

		// 编码为 PNG
		var pngBuffer bytes.Buffer
		if err = png.Encode(&pngBuffer, img); err != nil {
			return nil, fmt.Errorf("failed to encode PNG: %v", err), nil
		}

		imageResponse.Data = []ImageObject{
			{
				B64JSON: base64.RawStdEncoding.EncodeToString(pngBuffer.Bytes()),
				Seed:    request.Seed,
			},
		}
		return imageResponse, nil, pngBuffer.Bytes()
	} else if request.Model == "hidream" {
		// 将 jpg 转换为 png
		jpegReader := bytes.NewReader(data)
		img, errDecode := jpeg.Decode(jpegReader)
		if errDecode != nil {
			return nil, fmt.Errorf("failed to decode JPEG: %v", errDecode), nil
		}

		var pngBuffer bytes.Buffer
		if err = png.Encode(&pngBuffer, img); err != nil {
			return nil, fmt.Errorf("failed to encode PNG: %v", err), nil
		}

		imageResponse.Data = []ImageObject{
			{
				B64JSON: base64.RawStdEncoding.EncodeToString(pngBuffer.Bytes()),
				Seed:    request.Seed,
			},
		}
		return imageResponse, nil, pngBuffer.Bytes()
	} else if request.Model == "chroma" {
		// chroma 已经是 png 格式，保持不变
		imageResponse.Data = []ImageObject{
			{
				B64JSON: base64.RawStdEncoding.EncodeToString(data),
				Seed:    request.Seed,
			},
		}
		return imageResponse, nil, data
	}

	return nil, nil, nil
}

// ProcessFile handles file uploads
func (p *ChutesImageProvider) ProcessFile(ctx context.Context, file *multipart.FileHeader, purpose string) (string, error) {
	return "", errors.New("file processing not implemented for Chutes Stable Flow provider")
}

// Close closes any resources used by the provider
func (p *ChutesImageProvider) Close() error {
	return nil
}

func (p *ChutesImageProvider) getAPIKey(model models.Model) string {
	if len(model.ProviderAPIKey) == 0 {
		return ""
	}

	// 获取模型的计数器
	counterValue, _ := p.apiKeyCounters.LoadOrStore(model.ID, int64(0))
	counter := counterValue.(int64)

	// 计算索引并获取 API 密钥
	index := int(counter) % len(model.ProviderAPIKey)
	apiKey := model.ProviderAPIKey[index]

	// 增加计数器
	p.apiKeyCounters.Store(model.ID, counter+1)

	return apiKey
}

func (p *ChutesImageProvider) newHttpClient() *http.Client {
	// 创建一个 HTTP 传输。
	transport := &http.Transport{
		// 设置连接上下文。
		DialContext: (&net.Dialer{
			// 设置连接超时为 3 秒。
			Timeout: 3 * time.Second,
			// 设置保持连接存活。
			KeepAlive: 30 * time.Second,
		}).DialContext,
	}

	// 创建 HTTP 客户端，不设置全局超时。
	return &http.Client{
		// 设置传输。
		Transport: transport,
	}
}
