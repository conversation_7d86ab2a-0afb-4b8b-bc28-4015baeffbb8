package provider

//
// import (
// 	"context"
// 	"encoding/json"
// 	"errors"
// 	_const "github.com/luispater/ai-router/const"
// 	"github.com/luispater/ai-router/models"
// 	"gorm.io/gorm"
// 	"io"
// 	"mime/multipart"
// 	"time"
//
// 	"github.com/google/generative-ai-go/genai"
// 	"github.com/google/uuid"
// )
//
// // GeminiProvider implements the Provider interface for Google's Gemini models
// type GeminiProvider struct {
// 	OpenAICompatibility
// 	models map[string][]ModelType
// }
//
// // NewGeminiProvider creates a new Gemini provider
// func NewGeminiProvider(db *gorm.DB) (Provider, error) {
// 	// Define supported models and their capabilities
// 	m := map[string][]ModelType{}
//
// 	var providerModels []models.Model
// 	result := db.Where("provider_id=?", _const.ProviderGemini).Find(&providerModels)
// 	if result.Error != nil {
// 		return nil, nil
// 	}
//
// 	for _, model := range providerModels {
// 		m[model.Name] = []ModelType{ModelTypeChat, ModelTypeCompletion}
// 	}
//
// 	return &GeminiProvider{
// 		models: m,
// 	}, nil
// }
//
// // Register the provider
// func init() {
// 	RegisterProvider(_const.ProviderGemini, NewGeminiProvider)
// }
//
// // GetProviderType returns the type of the provider
// func (p *GeminiProvider) GetProviderType() _const.ProviderType {
// 	return _const.ProviderGemini
// }
//
// // GetSupportedModels returns the models supported by this provider
// func (p *GeminiProvider) GetSupportedModels() map[string][]ModelType {
// 	return p.models
// }
//
// // CreateChatCompletion creates a chat completion
// func (p *GeminiProvider) CreateChatCompletion(ctx context.Context, request []byte, model models.Model, usage *Usage) ([]byte, error) {
// 	p.SetBaseUrl("https://generativelanguage.googleapis.com/v1beta/openai")
// 	return p.OpenAICompatibility.CreateChatCompletion(ctx, request, model, usage)
// }
//
// // CreateChatCompletionStream creates a streaming chat completion
// func (p *GeminiProvider) CreateChatCompletionStream(ctx context.Context, request []byte, model models.Model, usage *Usage) (io.ReadCloser, error) {
// 	p.SetBaseUrl("https://generativelanguage.googleapis.com/v1beta/openai")
// 	return p.OpenAICompatibility.CreateChatCompletionStream(ctx, request, model, usage)
// }
//
// // CreateImage generates images based on a prompt
// func (p *GeminiProvider) CreateImage(ctx context.Context, request ImageGenerationRequest) (*ImageResponse, error) {
// 	// Gemini doesn't support image generation yet, return an error
// 	return nil, errors.New("image generation not supported by Gemini provider")
// }
//
// // ProcessFile handles file uploads
// func (p *GeminiProvider) ProcessFile(ctx context.Context, file *multipart.FileHeader, purpose string) (string, error) {
// 	// Gemini doesn't support file uploads in the same way as OpenAI, return an error
// 	return "", errors.New("file processing not supported by Gemini provider")
// }
//
// // Close closes any resources used by the provider
// func (p *GeminiProvider) Close() error {
// 	return nil
// }
//
// // Helper functions
//
// // convertMessagesToGeminiContent converts OpenAI messages to Gemini content
// func convertMessagesToGeminiContent(messages []Message) ([]*genai.Content, error) {
// 	var contents []*genai.Content
// 	var currentRole string
// 	var parts []genai.Part
//
// 	for i, msg := range messages {
// 		// Handle role changes
// 		if i == 0 || msg.Role != currentRole {
// 			// If we have accumulated parts, add them to contents
// 			if len(parts) > 0 {
// 				content := &genai.Content{
// 					Role:  mapRoleToGemini(currentRole),
// 					Parts: parts,
// 				}
// 				contents = append(contents, content)
// 				parts = nil
// 			}
// 			currentRole = msg.Role
// 		}
//
// 		// Add text content
// 		if msg.Content != "" {
// 			parts = append(parts, genai.Text(msg.Content))
// 		}
//
// 		// Add image content if available
// 		for _, imgData := range msg.Images {
// 			mimeType := "image/jpeg" // Default, could be improved with MIME detection
// 			parts = append(parts, genai.Blob{
// 				MIMEType: mimeType,
// 				Data:     imgData,
// 			})
// 		}
// 	}
//
// 	// Add any remaining parts
// 	if len(parts) > 0 {
// 		content := &genai.Content{
// 			Role:  mapRoleToGemini(currentRole),
// 			Parts: parts,
// 		}
// 		contents = append(contents, content)
// 	}
//
// 	return contents, nil
// }
//
// // mapRoleToGemini maps OpenAI roles to Gemini roles
// func mapRoleToGemini(role string) string {
// 	switch role {
// 	case "system":
// 		return "user" // Gemini doesn't have a system role, prepend to user
// 	case "assistant":
// 		return "model"
// 	case "user":
// 		return "user"
// 	default:
// 		return "user"
// 	}
// }
//
// // convertGeminiResponseToOpenAI converts a Gemini response to OpenAI format
// func convertGeminiResponseToOpenAI(resp *genai.GenerateContentResponse, model string, requestMessages []Message) (*ChatCompletionResponse, error) {
// 	if len(resp.Candidates) == 0 {
// 		return nil, errors.New("no candidates in response")
// 	}
//
// 	// Extract the text from the response
// 	var responseText string
// 	for _, part := range resp.Candidates[0].Content.Parts {
// 		if text, ok := part.(genai.Text); ok {
// 			responseText += string(text)
// 		}
// 	}
//
// 	// Create the OpenAI-compatible response
// 	response := &ChatCompletionResponse{
// 		ID:      uuid.New().String(),
// 		Object:  "chat.completion",
// 		Created: time.Now().Unix(),
// 		Model:   model,
// 		Choices: []ChatCompletionChoice{
// 			{
// 				Index: 0,
// 				Message: Message{
// 					Role:    "assistant",
// 					Content: responseText,
// 				},
// 				FinishReason: "stop", // Assuming normal completion
// 			},
// 		},
// 		Usage: Usage{
// 			// Gemini doesn't provide token counts, so we estimate
// 			PromptTokens:     int(resp.UsageMetadata.PromptTokenCount),
// 			CompletionTokens: int(resp.UsageMetadata.CandidatesTokenCount),
// 			TotalTokens:      int(resp.UsageMetadata.TotalTokenCount),
// 			PromptTokensDetails: UsagePromptTokensDetails{
// 				CachedTokens: int(resp.UsageMetadata.CachedContentTokenCount),
// 			},
// 		},
// 	}
//
// 	return response, nil
// }
//
// // convertGeminiChunkToOpenAI converts a Gemini streaming chunk to OpenAI format
// func convertGeminiChunkToOpenAI(resp *genai.GenerateContentResponse, id string, created int64, model string) (string, error) {
// 	if len(resp.Candidates) == 0 {
// 		return "", errors.New("no candidates in response")
// 	}
//
// 	// Extract the text from the response
// 	var responseText string
// 	for _, part := range resp.Candidates[0].Content.Parts {
// 		if text, ok := part.(genai.Text); ok {
// 			responseText += string(text)
// 		}
// 	}
//
// 	chunk := map[string]interface{}{
// 		"id":      id,
// 		"object":  "chat.completion.chunk",
// 		"created": created,
// 		"model":   model,
// 		"choices": []map[string]interface{}{
// 			{
// 				"index": 0,
// 				"delta": map[string]string{
// 					"content": responseText,
// 				},
// 				"finish_reason": nil,
// 			},
// 		},
// 	}
// 	byteChunk, _ := json.Marshal(chunk)
// 	return string(byteChunk), nil
//
// 	// Create the OpenAI-compatible chunk format directly as a string
// 	// Note: In a real implementation, you would use json.Marshal here
// 	// For simplicity, we're returning a formatted string
// 	// return fmt.Sprintf(`{"id":"%s","object":"chat.completion.chunk","created":%d,"model":"%s","choices":[{"index":0,"delta":{"content":"%s"},"finish_reason":null}]}`,
// 	// 	id, created, model, strings.ReplaceAll(responseText, "\"", "\\\"")), nil
// }
//
// func generateOpenAIFinishChunk(resp *genai.GenerateContentResponse, id string, created int64, model string) (string, error) {
// 	if len(resp.Candidates) == 0 {
// 		return "", errors.New("no candidates in response")
// 	}
//
// 	// Extract the text from the response
// 	var responseText string
// 	for _, part := range resp.Candidates[0].Content.Parts {
// 		if text, ok := part.(genai.Text); ok {
// 			responseText += string(text)
// 		}
// 	}
//
// 	chunk := map[string]interface{}{
// 		"id":      id,
// 		"object":  "chat.completion.chunk",
// 		"created": created,
// 		"model":   model,
// 		"choices": []map[string]interface{}{
// 			{
// 				"index":         0,
// 				"delta":         map[string]string{},
// 				"finish_reason": "stop",
// 			},
// 		},
// 	}
// 	byteChunk, _ := json.Marshal(chunk)
// 	return string(byteChunk), nil
// }
//
// func generateOpenAIUsageChunk(resp *genai.GenerateContentResponse, id string, created int64, model string) (string, error) {
// 	if len(resp.Candidates) == 0 {
// 		return "", errors.New("no candidates in response")
// 	}
//
// 	// Extract the text from the response
// 	var responseText string
// 	for _, part := range resp.Candidates[0].Content.Parts {
// 		if text, ok := part.(genai.Text); ok {
// 			responseText += string(text)
// 		}
// 	}
//
// 	chunk := map[string]interface{}{
// 		"id":      id,
// 		"object":  "chat.completion.chunk",
// 		"created": created,
// 		"model":   model,
// 		"choices": []map[string]interface{}{},
// 		"usage": map[string]interface{}{
// 			"prompt_tokens":     resp.UsageMetadata.PromptTokenCount,
// 			"completion_tokens": resp.UsageMetadata.CandidatesTokenCount,
// 			"total_tokens":      resp.UsageMetadata.TotalTokenCount,
// 			"prompt_tokens_details": map[string]interface{}{
// 				"cached_tokens": resp.UsageMetadata.CachedContentTokenCount,
// 			},
// 		},
// 	}
//
// 	byteChunk, _ := json.Marshal(chunk)
// 	return string(byteChunk), nil
// }
