package provider

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	_const "github.com/luispater/ai-router/const"
	"github.com/luispater/ai-router/models"
	"github.com/tidwall/gjson"
	"gorm.io/gorm"
	"io"
	"mime/multipart"
	"net"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"
)

// ComnergyImageProvider implements the Provider interface for OpenAI models
type ComnergyImageProvider struct {
	db             *gorm.DB
	apiKeyCounters sync.Map
}

// NewComnergyImageProvider creates a new OpenAI provider
func NewComnergyImageProvider(db *gorm.DB) (Provider, error) {
	// Define supported models and their capabilities
	var providerModels []models.Model
	result := db.Where("provider_id=?", _const.ProviderComnergy).Find(&providerModels)
	if result.Error != nil {
		return nil, result.Error
	}

	return &ComnergyImageProvider{
		db: db,
	}, nil
}

// Register the provider
func init() {
	RegisterProvider(_const.ProviderComnergy, NewComnergyImageProvider)
}

// GetProviderType returns the type of the provider
func (p *ComnergyImageProvider) GetProviderType() _const.ProviderType {
	return _const.ProviderChutes
}

// GetSupportedModels returns the models supported by this provider
func (p *ComnergyImageProvider) GetSupportedModels() map[string][]ModelType {
	return nil
}

// CreateChatCompletion creates a chat completion
func (p *ComnergyImageProvider) CreateChatCompletion(ctx context.Context, cancel context.CancelFunc, request []byte, model models.Model, usage *Usage) ([]byte, error, []byte) {
	return nil, errors.New("chat completion not supported by Chutes Stable Flow provider"), nil
}

// CreateChatCompletionStream creates a streaming chat completion
func (p *ComnergyImageProvider) CreateChatCompletionStream(ctx context.Context, cancel context.CancelFunc, request []byte, model models.Model, usage *Usage) (io.ReadCloser, error, []byte) {
	return nil, errors.New("chat completion not supported by Chutes Stable Flow provider"), nil
}

// CreateImage generates images based on a prompt
func (p *ComnergyImageProvider) CreateImage(ctx context.Context, model models.Model, request ImageGenerationRequest) (*ImageResponse, error, []byte) {
	generateImageURL := "https://flux.comnergy.com/api/generate"
	mapRequest := make(map[string]interface{})
	mapRequest["prompt"] = request.Prompt

	splitSize := strings.Split(request.Size, "x")
	if len(splitSize) != 2 {
		return nil, errors.New("invalid size format"), nil
	}
	width, err := strconv.Atoi(strings.Split(request.Size, "x")[0])
	if err != nil {
		return nil, fmt.Errorf("invalid width format: %v", err), nil
	}

	height, err := strconv.ParseInt(strings.Split(request.Size, "x")[1], 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid height format: %v", err), nil
	}
	mapRequest["width"] = width
	mapRequest["height"] = height
	mapRequest["guidance_scale"] = request.GuidanceScale
	mapRequest["cfg"] = request.GuidanceScale
	mapRequest["steps"] = request.Steps
	mapRequest["seed"] = request.Seed
	mapRequest["batch_size"] = 1

	bodyRequest, err := json.Marshal(mapRequest)
	if err != nil {
		return nil, err, nil
	}

	req, err := http.NewRequestWithContext(ctx, "POST", generateImageURL, bytes.NewBuffer(bodyRequest))
	// 如果创建请求失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	// 设置请求头。
	req.Header.Set("Content-Type", "application/json")

	// 使用 http.Client 发送请求。
	client := p.newHttpClient()
	resp, err := client.Do(req)
	// 如果发送请求失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	// 延迟关闭响应体。
	defer func() {
		err = resp.Body.Close()
	}()

	// 读取响应体。
	data, err := io.ReadAll(resp.Body)
	// 如果读取响应体失败，则返回错误。
	if err != nil {
		return nil, err, nil
	}

	// 检查响应状态码。
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %s", resp.Status), data
	}

	result := gjson.GetBytes(data, "imageUrl")
	if result.Type != gjson.String {
		return nil, fmt.Errorf("image data format error"), nil
	}
	splitData := strings.Split(result.String(), ",")
	if len(splitData) != 2 {
		return nil, fmt.Errorf("image output format error"), nil
	}

	imageResponse := new(ImageResponse)
	imageResponse.Created = time.Now().Unix()

	// chroma 已经是 png 格式，保持不变
	imageResponse.Data = []ImageObject{
		{
			B64JSON: splitData[1],
			Seed:    request.Seed,
		},
	}
	return imageResponse, nil, data

}

// ProcessFile handles file uploads
func (p *ComnergyImageProvider) ProcessFile(ctx context.Context, file *multipart.FileHeader, purpose string) (string, error) {
	return "", errors.New("file processing not implemented for Chutes Stable Flow provider")
}

// Close closes any resources used by the provider
func (p *ComnergyImageProvider) Close() error {
	return nil
}
func (p *ComnergyImageProvider) newHttpClient() *http.Client {
	// 创建一个 HTTP 传输。
	transport := &http.Transport{
		// 设置连接上下文。
		DialContext: (&net.Dialer{
			// 设置连接超时为 3 秒。
			Timeout: 3 * time.Second,
			// 设置保持连接存活。
			KeepAlive: 30 * time.Second,
		}).DialContext,
	}

	// 创建 HTTP 客户端，不设置全局超时。
	return &http.Client{
		// 设置传输。
		Transport: transport,
	}
}
