package provider

//
// import (
// 	"context"
// 	"errors"
// 	_const "github.com/luispater/ai-router/const"
// 	"github.com/luispater/ai-router/models"
// 	"gorm.io/gorm"
// 	"io"
// 	"mime/multipart"
// )
//
// // VolcengineProvider implements the Provider interface for OpenAI models
// type VolcengineProvider struct {
// 	OpenAICompatibility
// 	models map[string][]ModelType
// 	db     *gorm.DB
// }
//
// // NewVolcengineProvider creates a new OpenAI provider
// func NewVolcengineProvider(db *gorm.DB) (Provider, error) {
// 	// Define supported models and their capabilities
// 	m := map[string][]ModelType{}
//
// 	var providerModels []models.Model
// 	result := db.Where("provider_id=?", _const.ProviderVolcengine).Find(&providerModels)
// 	if result.Error != nil {
// 		return nil, result.Error
// 	}
//
// 	for _, model := range providerModels {
// 		var capabilities []ModelType
//
// 		if model.SupportsChat {
// 			capabilities = append(capabilities, ModelTypeChat)
// 		}
// 		if model.SupportsCompletion {
// 			capabilities = append(capabilities, ModelTypeCompletion)
// 		}
// 		if model.SupportsEmbedding {
// 			capabilities = append(capabilities, ModelTypeEmbedding)
// 		}
// 		if model.SupportsImageGen {
// 			capabilities = append(capabilities, ModelTypeImageGen)
// 		}
// 		if model.SupportsImageEdit {
// 			capabilities = append(capabilities, ModelTypeImageEdit)
// 		}
// 		if model.SupportsImageVar {
// 			capabilities = append(capabilities, ModelTypeImageVar)
// 		}
// 		if model.SupportsAudioTrans {
// 			capabilities = append(capabilities, ModelTypeAudioTrans)
// 		}
// 		if model.SupportsAudioTrans2 {
// 			capabilities = append(capabilities, ModelTypeAudioTrans2)
// 		}
//
// 		m[model.Name] = capabilities
// 	}
//
// 	return &VolcengineProvider{
// 		models: m,
// 		db:     db,
// 	}, nil
// }
//
// // Register the provider
// func init() {
// 	RegisterProvider(_const.ProviderVolcengine, NewVolcengineProvider)
// }
//
// // GetProviderType returns the type of the provider
// func (p *VolcengineProvider) GetProviderType() _const.ProviderType {
// 	return _const.ProviderVolcengine
// }
//
// // GetSupportedModels returns the models supported by this provider
// func (p *VolcengineProvider) GetSupportedModels() map[string][]ModelType {
// 	return p.models
// }
//
// // CreateChatCompletion creates a chat completion
// func (p *VolcengineProvider) CreateChatCompletion(ctx context.Context, request []byte, model models.Model, usage *Usage) ([]byte, error) {
// 	p.SetBaseUrl("https://ark.cn-beijing.volces.com/api/v3")
// 	return p.OpenAICompatibility.CreateChatCompletion(ctx, request, model, usage)
// }
//
// // CreateChatCompletionStream creates a streaming chat completion
// func (p *VolcengineProvider) CreateChatCompletionStream(ctx context.Context, request []byte, model models.Model, usage *Usage) (io.ReadCloser, error) {
// 	p.SetBaseUrl("https://ark.cn-beijing.volces.com/api/v3")
// 	return p.OpenAICompatibility.CreateChatCompletionStream(ctx, request, model, usage)
// }
//
// // CreateImage generates images based on a prompt
// func (p *VolcengineProvider) CreateImage(ctx context.Context, request ImageGenerationRequest) (*ImageResponse, error) {
// 	return nil, errors.New("image generation not supported by Volcengine provider")
// }
//
// // ProcessFile handles file uploads
// func (p *VolcengineProvider) ProcessFile(ctx context.Context, file *multipart.FileHeader, purpose string) (string, error) {
// 	return "", errors.New("file processing not implemented for Volcengine provider")
// }
//
// // Close closes any resources used by the provider
// func (p *VolcengineProvider) Close() error {
// 	return nil
// }
