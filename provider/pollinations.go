package provider

// curl https://text.pollinations.ai/openai \
// -H "Content-Type: application/json" \
// -o 1.base64 \
// -d '{
// "model": "openai-audio",
// "modalities": ["text", "audio"],
// "audio": { "voice": "ash", "format": "mp3" },
// "messages": [
// {"role": "system", "content": "你是一个复读机，你只会重复用户输入的话"},
// {"role": "user", "content": "Alright, team, let''s bring the energy—time to move, sweat, and feel amazing!\\nWe''re starting with a dynamic warm-up, so roll those shoulders, stretch it out, and get that body ready! Now, into our first round—squats, lunges, and high knees—keep that core tight, push through, you got this!\\nHalfway there, stay strong—breathe, focus, and keep that momentum going! Last ten seconds, give me everything you''ve got!\\nAnd… done! Take a deep breath, shake it out—you crushed it! Stay hydrated, stay moving, and I''ll see you next time!"}
// ],
// "private": true
// }'
//
// cat 1.base64 | jq -r .choices[0].message.audio.data | base64 -d -o 1.mp3

// curl https://text.pollinations.ai/openai \
// -H "Content-Type: application/json" \
// -o 1.base64 \
// -d '{
// "model": "openai-audio",
// "modalities": ["text", "audio"],
// "temperature": 0,
// "audio": { "voice": "ash", "format": "mp3" },
// "messages": [
// {"role": "system", "content": "你是一个单词拼读机，你需要把输入<speak>标签里的单词一个字母一个字母的拼读出来，语速平缓，间隔固定，然后再整体读一遍这个单词"},
// {"role": "user", "content": "<speak>message</speak>"}
// ],
// "private": true
// }'

// curl https://text.pollinations.ai/openai \
// -H "Content-Type: application/json" \
// -o 1.base64 \
// -d '{
// "model": "openai-audio",
// "modalities": ["text", "audio"],
// "audio": { "voice": "ash", "format": "mp3" },
// "messages": [
// {"role": "system", "content": "你是一个英文字朗读母机，你需要把输入<speak>标签里的字母一个一个读出来，而不作为单词拼读"},
// {"role": "user", "content": "<speak>ABCDEFGHIJKLMNOPQRSTUVWXYZ</speak>"}
// ],
// "private": true
// }'

// curl 'https://deep.ch-at.pw/proxy.php?action=sendMessage' \
// --data-raw '{"model":"deepseek/deepseek-v3/community","max_tokens":4000,"top_p":0.6,"temperature":0.6,"frequency_penalty":0,"presence_penalty":0,"messages":[{"role":"user","content":"who are you?"}],"stream":true}'

// curl https://text.pollinations.ai/openai \
// -H "Content-Type: application/json" \
// -d '{
// "model": "gemini",
// "messages": [
// {"role": "system", "content": "You are a helpful assistant."},
// {"role": "user", "content": "给我说一个200字的故事"}
// ],
// "max_tokens": 8192
// }'
//
//
// curl https://text.pollinations.ai/openai \
// -H "Content-Type: application/json" \
// -d '{
// "model": "openai",
// "messages": [
// {"role": "system", "content": "You are a helpful assistant."},
// {"role": "user", "content": "给我说一个200字的故事"}
// ],
// "max_tokens": 8192
// }'
