package main

import (
	"context"
	"errors"
	"log"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"

	"github.com/luispater/ai-router/api"
	"github.com/luispater/ai-router/billing"
	"github.com/luispater/ai-router/config"
	"github.com/luispater/ai-router/core"
	"github.com/luispater/ai-router/models"
	"github.com/luispater/ai-router/provider"
	"github.com/luispater/ai-router/router"
)

const (
	configFile = "config.yaml"
)

// / main 函数是应用程序的入口点。
func main() {
	// gin.SetMode(gin.ReleaseMode)

	// 设置日志的格式，包括标准标志和短文件名。
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	// 打印启动信息。
	log.Println("Starting AI Router...")

	// 加载配置文件。
	cfg, err := config.LoadConfig(configFile)
	// 如果加载配置失败，则记录错误并退出。
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// 连接到数据库。
	db, err := models.ConnectDB(cfg.Database.URL)
	// 如果连接数据库失败，则记录错误并退出。
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 初始化数据库模式。
	if err = models.InitDB(db); err != nil {
		// 如果初始化数据库模式失败，则记录错误并退出。
		log.Fatalf("Failed to initialize database schema: %v", err)
	}

	// 如果需要，播种默认数据。
	if err = models.SeedDefaultProviders(db); err != nil {
		// 如果播种默认数据失败，则记录警告信息。
		log.Printf("Warning: Failed to seed default providers: %v", err)
	}

	// 创建一个 map 来存储每个 API 密钥的速率限制器。
	rateLimiters := make(map[uint]*core.RateLimiter)
	// 创建一个互斥锁来保护速率限制器 map 的并发访问。
	rateLimitersMutex := &sync.Mutex{}

	// 注册提供者。
	providerRegistry := provider.ProviderRegistry

	// 初始化 Redis 客户端
	redisClient := billing.GetRedisClient(&cfg.Redis)
	if redisClient == nil {
		log.Printf("Warning: Failed to initialize Redis client")
	} else {
		// 初始化 Redis 速率限制器
		api.InitRedisRateLimiter(redisClient)
		log.Println("Redis rate limiter initialized")
	}

	// 初始化计费队列。
	var billingProcessor *billing.RedisBillingProcessor
	// 初始化计费队列，如果失败则使用数据库直接存储
	billingQueue, err := billing.InitBillingQueue(&cfg.Redis, db)
	if err != nil {
		// 如果初始化计费队列失败，则记录警告信息。
		log.Printf("Warning: Failed to initialize billing queue: %v", err)
		// 打印信息，说明计费信息将直接保存到数据库。
		log.Println("Billing information will be saved directly to the database")
	} else {
		// 设置全局计费队列。
		billing.SetGlobalBillingQueue(billingQueue)

		// 创建并启动计费处理器。
		billingProcessor = billing.NewRedisBillingProcessor(billingQueue, db)
		// 创建一个带有取消功能的上下文。
		ctx, cancel := context.WithCancel(context.Background())
		// 延迟取消上下文。
		defer cancel()
		// 启动计费处理器。
		billingProcessor.Start(ctx)
		// 打印计费处理器已启动的信息。
		log.Println("Billing processor started")
	}

	// 创建 Gin 路由器。
	r := router.SetupRouter(db, providerRegistry, rateLimiters, rateLimitersMutex)

	// 创建 HTTP 服务器。
	server := &http.Server{
		// 设置服务器的监听地址。
		Addr: ":" + cfg.Server.Port,
		// 设置服务器的处理器。
		Handler: r,
	}

	// 在 goroutine 中启动服务器。
	go func() {
		// 打印服务器监听端口的信息。
		log.Printf("Server listening on port %s", cfg.Server.Port)
		// 启动服务器并监听，如果发生错误且不是服务器关闭错误，则记录错误并退出。
		if errListenAndServe := server.ListenAndServe(); errListenAndServe != nil && !errors.Is(errListenAndServe, http.ErrServerClosed) {
			log.Fatalf("Failed to start server: %v", errListenAndServe)
		}
	}()

	// 等待中断信号以优雅地关闭服务器。
	quit := make(chan os.Signal, 1)
	// 监听 SIGINT 和 SIGTERM 信号。
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	// 阻塞直到接收到信号。
	<-quit
	// 打印服务器正在关闭的信息。
	log.Println("Shutting down server...")

	// 为关闭操作创建一个截止时间。
	ctx, cancel := context.WithTimeout(context.Background(), cfg.Server.ShutdownTimeout)
	// 延迟取消上下文。
	defer cancel()

	// 关闭服务器。
	if err = server.Shutdown(ctx); err != nil {
		// 如果服务器强制关闭，则记录错误并退出。
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	// 关闭所有速率限制器。
	rateLimitersMutex.Lock()
	// 遍历所有的速率限制器
	for _, limiter := range rateLimiters {
		// 关闭速率限制器
		limiter.Close()
	}
	// 解锁互斥锁。
	rateLimitersMutex.Unlock()

	// 关闭计费队列和处理器。
	if billingProcessor != nil {
		// 停止计费处理器。
		billingProcessor.Stop()
		// 打印计费处理器已停止的信息。
		log.Println("Billing processor stopped")
	}
	if billingQueue != nil {
		// 关闭计费队列。
		err = billingQueue.Close()
		// 打印计费队列已关闭的信息。
		log.Println("Billing queue closed")
	}

	// 打印服务器已正常退出的信息。
	log.Println("Server exited properly")
}
