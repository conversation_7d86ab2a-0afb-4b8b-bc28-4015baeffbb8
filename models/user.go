package models

import (
	"time"

	"gorm.io/gorm"
)

// / User represents a user of the API
// / User 表示 API 的一个用户
type User struct {
	// ID 主键
	ID uint `gorm:"primarykey" json:"id"`
	// CreatedAt 创建时间
	CreatedAt time.Time `json:"created_at"`
	// UpdatedAt 更新时间
	UpdatedAt time.Time `json:"updated_at"`
	// DeletedAt 删除时间，用于软删除
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// Email 用户的电子邮箱，唯一且不能为空
	Email string `gorm:"size:255;not null;uniqueIndex" json:"email"`
	// Name 用户的名称
	Name string `gorm:"size:100" json:"name"`
	// IsActive 用户是否激活
	IsActive bool `gorm:"index;default:true" json:"is_active"`
	// IsAdmin 用户是否是管理员
	IsAdmin bool `gorm:"index;default:false" json:"is_admin"`
	// Credits 用户剩余余额
	Credits float64 `gorm:"default:0" json:"credits"`

	// Relationships
	// APIKeys 用户拥有的 API 密钥列表
	APIKeys []APIKey `gorm:"foreignKey:UserID" json:"-"`
}
