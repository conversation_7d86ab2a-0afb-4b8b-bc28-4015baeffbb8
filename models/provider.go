package models

import (
	"time"

	"gorm.io/gorm"
)

// Provider represents an AI provider
// Provider 表示一个 AI 提供者
type Provider struct {
	// ID 主键
	ID uint `gorm:"primarykey" json:"id"`
	// CreatedAt 创建时间
	CreatedAt time.Time `json:"created_at"`
	// UpdatedAt 更新时间
	UpdatedAt time.Time `json:"updated_at"`
	// DeletedAt 删除时间，用于软删除
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// Name 提供者名称
	Name string `gorm:"size:50;not null;uniqueIndex" json:"name"`
	// Description 提供者描述
	Description string `gorm:"size:255" json:"description"`
	// IsActive 是否激活
	IsActive bool `gorm:"index;default:true" json:"is_active"`

	// Provider-specific configuration stored as JSON
	// 以 JSON 格式存储的提供者特定配置
	Config string `gorm:"type:jsonb" json:"-"`
}
