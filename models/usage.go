package models

import "time"

// Usage 跟踪 API 的使用情况，用于计费和速率限制
type Usage struct {
	// ID 主键
	ID uint `gorm:"primarykey" json:"id"`
	// CreatedAt 创建时间
	CreatedAt time.Time `json:"created_at"`

	// Request details
	// RequestType 请求类型，例如：chat, completion, embedding 等
	RequestType string `gorm:"size:50;not null" json:"request_type"`
	// PromptTokens 提示令牌数
	PromptTokens int `json:"prompt_tokens"`
	// OutputTokens 输出令牌数
	OutputTokens int `json:"output_tokens"`
	// TotalTokens 总令牌数
	TotalTokens int `json:"total_tokens"`
	// ProcessingMs 处理请求所花费的时间，单位为毫秒
	ProcessingMs int `json:"processing_ms"`
	// StatusCode HTTP 状态码
	StatusCode int `json:"status_code"`
	// ErrorMessage 错误消息
	ErrorMessage string `gorm:"size:255" json:"error_message"`

	// Cost calculation
	// Cost 成本，单位为美元
	Cost float64 `json:"cost"`

	// Relationships
	// UserID 用户 ID
	UserID uint `gorm:"index" json:"user_id"`
	// User 用户
	User User `json:"-"`
	// APIKeyID API 密钥 ID
	APIKeyID uint `gorm:"index" json:"api_key_id"`
	// APIKey API 密钥
	APIKey APIKey `json:"-"`
	// ModelID 模型 ID
	ModelID uint `gorm:"index" json:"model_id"`
	// Model 模型
	Model Model `json:"-"`
}
