package models

import (
	"time"

	"gorm.io/gorm"
)

// Model 表示来自提供者的 AI 模型
type Model struct {
	// ID 主键
	ID uint `gorm:"primarykey" json:"id"`
	// CreatedAt 创建时间
	CreatedAt time.Time `json:"created_at"`
	// UpdatedAt 更新时间
	UpdatedAt time.Time `json:"updated_at"`
	// DeletedAt 删除时间，用于软删除
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// Name 模型名称
	Name string `gorm:"size:100;not null;index" json:"name"`
	// ProviderModelName 提供者模型名称
	ProviderModelName string `gorm:"size:100;not null" json:"provider_name"`
	// DisplayName 显示名称
	DisplayName string `gorm:"size:100" json:"display_name"`
	// Description 描述
	Description string `gorm:"size:255" json:"description"`

	// Model capabilities
	// SupportsChat 是否支持聊天
	SupportsChat bool `gorm:"default:false" json:"supports_chat"`
	// SupportsCompletion 是否支持补全
	SupportsCompletion bool `gorm:"default:false" json:"supports_completion"`
	// SupportsEmbedding 是否支持嵌入
	SupportsEmbedding bool `gorm:"default:false" json:"supports_embedding"`
	// SupportsImageGen 是否支持图像输入
	SupportsInputImage bool `gorm:"default:false" json:"supports_input_image"`
	// SupportsImageGen 是否支持图像生成
	SupportsImageGen bool `gorm:"default:false" json:"supports_image_gen"`
	// SupportsImageEdit 是否支持图像编辑
	SupportsImageEdit bool `gorm:"default:false" json:"supports_image_edit"`
	// SupportsImageVar 是否支持图像变体
	SupportsImageVar bool `gorm:"default:false" json:"supports_image_var"`
	// SupportsAudioTrans 是否支持音频转录
	SupportsAudioTrans bool `gorm:"default:false" json:"supports_audio_trans"`
	// SupportsAudioTrans2 是否支持音频翻译
	SupportsAudioTrans2 bool `gorm:"default:false" json:"supports_audio_trans2"`

	SupportGoogleThinking bool `gorm:"default:false" json:"supports_google_thinking"`

	// Rate limiting
	// RPM 每分钟请求数
	RPM int `gorm:"default:0" json:"rpm"` // Requests per minute
	// RPH 每小时请求数
	RPH int `gorm:"default:0" json:"rph"` // Requests per hour
	// RPD 每天请求数
	RPD int `gorm:"default:0" json:"rpd"` // Requests per day
	// TPM 每分钟令牌数
	TPM int `gorm:"default:0" json:"tpm"` // Tokens per minute
	// TPH 每小时令牌数
	TPH int `gorm:"default:0" json:"tph"` // Tokens per hour
	// TPD 每天令牌数
	TPD int `gorm:"default:0" json:"tpd"` // Tokens per day

	// MaxTokens 最大输出令牌数
	MaxTokens int `gorm:"default:0" json:"max_tokens"` // Max output tokens
	// ContextLength 最大上下文长度
	ContextLength       int      `gorm:"default:0" json:"context_length"` // Max context length
	SupportedParameters []string `gorm:"type:jsonb;serializer:json" json:"supported_parameters"`

	// 价格 (美元)
	// InputPricePerToken 每个输入令牌的价格（美元）
	InputPricePerToken float64 `gorm:"default:0;index" json:"input_price_per_token"`
	// OutputPricePerToken 每个输出令牌的价格（美元）
	OutputPricePerToken float64 `gorm:"default:0;index" json:"output_price_per_token"`

	// Relationships
	// ProviderID 提供者 ID
	ProviderID uint `gorm:"index" json:"provider_id"`
	// Provider 提供者
	Provider Provider `json:"-"`
	// BaseURL 基础 URL
	BaseURL string `gorm:"size:255" json:"base_url"`
	// BaseURLDirect 是否直接使用基础 URL
	BaseURLDirect bool `gorm:"default:false" json:"base_url_direct"`
	// IsOpenAICompatibility 是否兼容 OpenAI 格式
	IsOpenAICompatibility bool `gorm:"default:true" json:"is_openai_compatibility"`
	// StreamOnly 是否仅支持流式传输
	StreamOnly bool `gorm:"default:false" json:"stream_only"`

	// ProviderAPIKey 此模型的提供者 API 密钥列表
	ProviderAPIKey []string `gorm:"type:jsonb;serializer:json" json:"-"`
	// Enabled 是否启用
	Enabled bool `gorm:"index;default:true" json:"enabled"`
	// Visible 是否展示
	Visible bool `gorm:"index;default:true" json:"visible"`
}

type DisplayModel struct {
	ID                  string   `json:"id"`
	Name                string   `json:"name"`
	Object              string   `json:"object"`
	Description         string   `json:"description"`
	SupportedParameters []string `json:"supported_parameters"`
	ContextLength       int      `json:"context_length,omitempty"`
	MaxCompletionTokens int      `json:"max_completion_tokens,omitempty"`
	Architecture        struct {
		Modality         string   `json:"modality"`
		InputModalities  []string `json:"input_modalities"`
		OutputModalities []string `json:"output_modalities"`
	} `json:"architecture"`
	Pricing struct {
		Prompt     string `json:"prompt"`
		Completion string `json:"completion"`
	} `json:"pricing,omitempty"`
}
