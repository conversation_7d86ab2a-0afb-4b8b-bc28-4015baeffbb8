package models

import (
	"time"

	"gorm.io/gorm"
)

type APIKey struct {
	// ID 主键
	ID uint `gorm:"primarykey" json:"id"`
	// CreatedAt 创建时间
	CreatedAt time.Time `json:"created_at"`
	// UpdatedAt 更新时间
	UpdatedAt time.Time `json:"updated_at"`
	// DeletedAt 删除时间，用于软删除
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// Key 加密的 API 密钥
	Key string `gorm:"size:255;not null;uniqueIndex" json:"-"`
	// Name API 密钥的名称
	Name string `gorm:"size:100" json:"name"`
	// ExpiresAt 过期时间
	ExpiresAt time.Time `json:"expires_at"`
	// IsActive 是否激活
	IsActive bool `gorm:"index;default:true" json:"is_active"`

	// RPS 每秒请求数，如果为 0，则使用模型的默认值
	RPS int `gorm:"default:0" json:"rps"`
	// TPS 每秒令牌数，如果为 0，则使用模型的默认值
	TPS int `gorm:"default:0" json:"tps"`
	// RPM 每分钟请求数，如果为 0，则使用模型的默认值
	RPM int `gorm:"default:0" json:"rpm"`
	// TPM 每分钟令牌数，如果为 0，则使用模型的默认值
	TPM int `gorm:"default:0" json:"tpm"`
	// RPH 每小时请求数，如果为 0，则使用模型的默认值
	RPH int `gorm:"default:0" json:"rph"`
	// TPH 每小时令牌数，如果为 0，则使用模型的默认值
	TPH int `gorm:"default:0" json:"tph"`
	// RPD 每天请求数，如果为 0，则使用模型的默认值
	RPD int `gorm:"default:0" json:"rpd"`
	// TPD 每天令牌数，如果为 0，则使用模型的默认值
	TPD int `gorm:"default:0" json:"tpd"`

	// Relationships
	// UserID 用户 ID
	UserID uint `gorm:"index" json:"user_id"`
	// User 用户
	User User `json:"-"`
	// Usage 使用记录
	Usage []Usage `gorm:"foreignKey:APIKeyID" json:"-"`
}
