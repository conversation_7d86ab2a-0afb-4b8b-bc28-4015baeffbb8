package _const

// ProviderType represents the type of AI provider
type ProviderType uint

const (
	ProviderOpenAICompatibility ProviderType = 0
	ProviderGemini              ProviderType = 1
	ProviderOpenAI              ProviderType = 2
	ProviderVolcengine          ProviderType = 3
	ProviderOpenRouter          ProviderType = 4
	ProviderDeepInfraChat       ProviderType = 5
	ProviderTypeGpt             ProviderType = 6
	ProviderGlider              ProviderType = 7
	ProviderChutes              ProviderType = 8
	ProviderXAi                 ProviderType = 9
	ProviderCerebras            ProviderType = 10
	ProviderSambanova           ProviderType = 11
	ProviderAlibaba             ProviderType = 12
	ProviderComnergy            ProviderType = 13
	ProviderNineteen            ProviderType = 14
	// Add more providers as needed
)

var (
	TagNoData       = []byte{58}
	TagData         = []byte("data: ")
	TagDataDone     = []byte("[DONE]")
	TagPromptTokens = []byte("prompt_tokens")
)

var SambanovaProviderAPIKey = []string{"900a92ef-6447-429b-bce4-353eef52b51f", "5047328d-9291-4b24-8b40-8fae3246ff4e"}

var ChutesProviderAPIKey = []string{
	"cpk_f7756d5d5c204bc4a08023b4a7d7758a.3c518b2303915f70ba1254c9c6c2b698.CXHSXHZyRUZb0ss3JHOj8GsT6badQogy",
	"cpk_14c6b5a1695849f8b81d420fc9893bf1.94812c750ddb5cb08e944a558364a36e.wUZvbf7WCmhH67wR2ZCuWu2LW6Xrncwb",
	"cpk_b312db3bcef746bcb60fca60bf43ac48.258ad30aa353566dbcc15c7109e17daf.dFu8ScxrBNfzzTPEaLwrCJpYPUNxMgDA",
	"cpk_24c549bef2884cbd93546275d14a567f.689154cbbb745009a3d77fc34209214b.u0Z2CFDfpIV0D62ECHKGZ371WfLdXjDb",
	"cpk_cbe71da277c749838ed2aa1fcf6d8994.fb4be258080c5c0ba98d5bd29806b70b.UttyiABDdKW9g7umZ0D26idyCVdkR39D",
}
